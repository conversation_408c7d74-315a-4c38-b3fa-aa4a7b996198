# Florida Drug Overdose Analytics App - Complete Code Explanation
## Written for 7th Grade Understanding

---

## **PART 1: IMPORTING TOOLS (Lines 1-16)**

Think of importing like getting tools from a toolbox. Each tool helps our program do different things.

**Line 1:** `import streamlit as st`
- This gets a tool called Streamlit that helps us make websites and apps
- We call it "st" for short, like a nickname

**Line 2:** `import pandas as pd`
- This gets a tool called Pandas that helps us work with data (like Excel spreadsheets)
- We call it "pd" for short

**Line 3:** `import matplotlib.pyplot as plt`
- This gets a tool for making basic charts and graphs
- We call it "plt" for short

**Line 4:** `import seaborn as sns`
- This gets another tool for making prettier charts
- We call it "sns" for short

**Line 5:** `import re`
- This gets a tool called "regular expressions" that helps us find patterns in text
- Like finding all phone numbers in a document

**Line 6:** `import plotly.express as px`
- This gets a tool for making interactive charts (charts you can click on)
- We call it "px" for short

**Line 7:** `import plotly.graph_objects as go`
- This gets more advanced tools for making interactive charts
- We call it "go" for short

**Line 8:** `import plotly.figure_factory as ff`
- This gets tools for making special types of charts
- We call it "ff" for short

**Line 9:** `import os`
- This gets a tool that helps us work with files and folders on the computer

**Line 10:** `import requests`
- This gets a tool that helps us download things from the internet

**Line 11:** `import json`
- This gets a tool that helps us work with a special type of data format called JSON

**Line 12:** `import numpy as np`
- This gets a tool that helps us do math with lots of numbers at once
- We call it "np" for short

**Line 13:** `from scipy.stats import pearsonr, spearmanr, chi2_contingency`
- This gets special math tools for statistics (finding relationships between things)
- pearsonr and spearmanr help find if two things are related
- chi2_contingency helps test if things are independent

**Line 14:** `from difflib import get_close_matches`
- This gets a tool that helps find words that are similar to each other
- Like finding "fentanil" when someone meant "fentanyl"

**Line 15:** `import warnings`
- This gets a tool that controls warning messages

**Line 16:** `warnings.filterwarnings('ignore')`
- This tells the program to hide warning messages so they don't clutter our screen

---

## **PART 2: SETTING UP COLORS AND PAGE (Lines 18-21)**

**Line 18:** `PRIMARY_COLOR = "#0021A5"`
- This creates a variable (like a box) that holds a blue color code
- Color codes are like recipes for colors on computers

**Line 19:** `SECONDARY_COLOR = "#FA4616"`
- This creates a variable that holds an orange color code

**Line 20:** `ACCENT_COLOR = "#28A745"`
- This creates a variable that holds a green color code

**Line 21:** `st.set_page_config(page_title="Enhanced Florida Drug Overdose Analytics", layout="wide")`
- This tells Streamlit to set up our webpage
- It gives it a title and makes it use the full width of the screen

---

## **PART 3: LOADING THE DATA (Lines 23-40)**

**Line 23:** `@st.cache_data`
- This is like putting a sticky note that says "remember this"
- It makes the computer remember the data so it doesn't have to reload it every time

**Line 24:** `def load_data():`
- This starts creating a function (like a recipe) called "load_data"
- Functions are sets of instructions that we can use over and over

**Line 25:** `"""Load and preprocess the drug overdose data"""`
- This is a comment that explains what this function does
- Comments are notes for humans, the computer ignores them

**Line 26:** `df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)`
- This tells pandas to read a CSV file (like an Excel file) and put it in a variable called "df"
- "low_memory=False" tells it to use more computer memory to read the file faster

**Line 27:** `df.columns = df.columns.str.strip().str.lower()`
- This cleans up the column names by removing extra spaces and making them lowercase
- Like changing "  AGE  " to "age"

**Line 28:** `df['date'] = pd.to_datetime(df['date'], errors='coerce')`
- This converts the date column to a proper date format
- "errors='coerce'" means if it can't convert something, just make it blank

**Line 29:** `df['year'] = df['date'].dt.year`
- This creates a new column called 'year' by extracting just the year from the date
- Like getting "2022" from "2022-05-15"

**Lines 31-35:** Creating age groups
- This creates a new column that puts people into age groups
- Like putting someone who is 25 into the "25-34" group
- `pd.cut()` is like sorting people into bins based on their age

**Line 37:** `return df`
- This gives back the cleaned-up data to whoever asked for it

**Line 39:** `df = load_data()`
- This actually runs our function and stores the result in a variable called "df"

---

## **PART 4: LOADING MAP DATA (Lines 41-58)**

**Line 41:** `@st.cache_data`
- Again, this tells the computer to remember this data

**Line 42:** `def load_geojson():`
- This starts a function to load map data for Florida counties

**Line 43:** `"""Load Florida counties GeoJSON for mapping"""`
- Comment explaining what this function does

**Line 44:** `filename = "geojson-fl-counties-fips.json"`
- This creates a variable with the name of the file we want

**Line 45:** `if not os.path.exists(filename):`
- This checks if the file exists on our computer
- Like checking if a book is on your shelf

**Lines 46-52:** Downloading the file if it doesn't exist
- If the file isn't there, download it from the internet
- Like ordering a book online if you don't have it

**Lines 53-58:** Loading and filtering the map data
- This reads the map file and keeps only Florida counties
- Like taking a map of the whole US and cutting out just Florida

---

## **PART 5: ORGANIZING OUR DATA (Lines 60-86)**

These lines create lists that organize our data into different categories, like sorting your clothes into different drawers.

**Lines 60-66:** `non_drug_columns = [...]`
- This creates a list of columns that are NOT about drugs
- Like age, sex, race, county, date, etc.
- These are background information about each person

**Line 68:** `drug_columns = [col for col in df.columns if col not in non_drug_columns]`
- This creates a list of columns that ARE about drugs
- It takes all columns and removes the non-drug ones
- Like making a list of all your toys by removing everything that's not a toy

**Line 70:** `demographic_columns = ['age', 'age_group', 'sex', 'race', 'county name', 'manner of death']`
- This creates a list of columns about people's characteristics

**Line 73:** `temporal_columns = ['date', 'year']`
- This creates a list of columns about time

**Line 76:** `aggregate_columns = ['causesubstancecount', 'presentsubstancecount', 'totalsubstancecount', 'poly']`
- This creates a list of columns that count things

**Lines 79-80:** `group_columns = [...]`
- This creates a list of columns that group drugs into categories
- Like grouping all pain medicines together

---

## **PART 6: CREATING THE SIDEBAR HELP (Lines 82-149)**

This section creates the help menu that appears on the left side of our app.

**Line 82:** `with st.sidebar:`
- This tells Streamlit that everything inside this block goes in the sidebar

**Line 83:** `st.markdown(f"<h2 style='color:{PRIMARY_COLOR}'>🔎 Enhanced Query Help</h2>", unsafe_allow_html=True)`
- This creates a heading in the sidebar with our blue color
- `f"..."` lets us put variables inside text
- `unsafe_allow_html=True` lets us use HTML code for styling

**Lines 85-93:** Visualization Types section
- This creates a section explaining different types of charts
- Each line shows an example of how to ask for that type of chart

**Lines 95-101:** Cause vs Present section
- This explains the difference between deaths CAUSED by a drug vs deaths with the drug PRESENT
- Like the difference between dying FROM something vs dying WITH something

**Lines 103-109:** Demographics section
- This explains how to filter by age, sex, race, and location

**Lines 111-115:** Correlations section
- This explains how to ask about relationships between things

**Lines 117-125:** Drug Summary Tables section
- This explains how to ask for tables showing all drugs

**Lines 127-135:** Example Queries section
- This shows specific examples of questions you can ask

---

## **PART 7: CREATING THE MAIN TITLE (Lines 137-149)**

**Lines 137-140:** Main title
- This creates the big title at the top of the page in blue

**Lines 142-145:** Subtitle
- This creates a smaller description under the title in orange

**Lines 147-148:** Input box
- This creates the text box where users type their questions
- The `help` parameter shows a tooltip when you hover over it

---

## **PART 8: FUZZY MATCHING FUNCTION (Lines 150-165)**

This function helps when users make spelling mistakes.

**Line 150:** `def fuzzy_match_drug(input_drug, drug_list, threshold=0.6):`
- This starts a function that finds similar drug names
- `threshold=0.6` means it needs to be at least 60% similar

**Line 152:** `if input_drug.lower() in [drug.lower() for drug in drug_list]:`
- This first checks if there's an exact match (ignoring uppercase/lowercase)

**Line 153:** `return next(drug for drug in drug_list if drug.lower() == input_drug.lower())`
- If there's an exact match, return it

**Lines 156-157:** `matches = get_close_matches(...)`
- If no exact match, try to find similar words
- Like finding "fentanyl" when someone types "fentanil"

**Lines 158-161:** Return the best match
- If a similar word is found, return the correct spelling

**Line 163:** `return None`
- If nothing similar is found, return nothing

---

## **PART 9: QUERY VALIDATION FUNCTION (Lines 167-217)**

This function checks if a user's question makes sense.

**Line 167:** `def validate_query(text):`
- This starts a function to check if a question is valid

**Line 169:** `text_lower = text.lower().strip()`
- This converts the text to lowercase and removes extra spaces

**Lines 172-173:** Check for very short queries
- If someone types just one letter, that's not a real question

**Lines 176-177:** Check for random strings
- This uses a pattern to detect gibberish like "asdfgh"
- It looks for strings with no vowels and too many consonants in a row

**Lines 180-182:** Check for error words
- If someone types "error" or "test", that's probably not a real question

**Lines 185-200:** Check for meaningful keywords
- This creates a list of words that should be in a real question about drugs
- Like "chart", "deaths", "fentanyl", "age", etc.

**Lines 202-203:** Check if the question contains meaningful words
- If the question doesn't have any meaningful keywords, it's probably not valid

**Lines 205-210:** Return error message if invalid
- If the question is invalid, return an error message with suggestions

**Line 212:** `return True, ""`
- If the question passes all tests, say it's valid

---

---

## **PART 10: MAIN QUERY PARSING FUNCTION (Lines 219-413)**

This is the "brain" of our app that figures out what the user wants.

**Line 219:** `def parse_query(text):`
- This starts the main function that understands user questions

**Line 221:** `text_lower = text.lower()`
- Convert the question to lowercase for easier processing

**Line 222:** `query = {"original_text": text_lower}`
- Create a dictionary (like a box with labeled compartments) to store what we figure out

### **Finding Years in the Question (Lines 224-231)**

**Line 225:** `year_matches = re.findall(r"(19\d{2}|20\d{2})", text)`
- This looks for 4-digit years like 1995, 2022, 2023
- `\d{2}` means "exactly 2 digits"
- So this finds years from 1900-1999 and 2000-2099

**Lines 226-231:** Store the years we found
- If we found years, put them in our query box
- If we found multiple years, use the first one as the main year

### **Finding Drugs in the Question (Lines 233-284)**

**Line 233:** `found_drugs = []`
- Create an empty list to store drugs we find

**Lines 236-239:** First, look for exact matches
- Go through each drug name and see if it appears exactly in the question

**Lines 242-268:** If no exact matches, try aliases
- Create a dictionary of drug nicknames and common misspellings
- Like "meth" for "methamphetamine" or "fentanil" for "fentanyl"
- Check if any of these nicknames appear in the question

**Lines 271-279:** If still no matches, try fuzzy matching
- Extract words that are 4+ letters long from the question
- Use our fuzzy matching function to see if any are similar to drug names
- Like finding "fentanyl" when someone types "fentanil"

**Lines 282-284:** Handle fentanyl vs fentanyl analogs
- These are treated as separate drugs based on user memory
- If someone says "fentanyl" without "analog", only show regular fentanyl

### **Understanding Cause vs Present (Lines 286-306)**

**Lines 287-288:** Create lists of keywords
- "cause", "caused", "from" suggest deaths CAUSED by the drug
- "present", "detected", "with" suggest deaths WITH the drug present

**Lines 290-291:** Check which keywords appear in the question

**Lines 294-299:** Handle special phrases
- "deaths caused by" clearly means caused deaths
- "deaths with" clearly means deaths with drug present

**Lines 301-306:** Decide what to search for
- If both cause and present keywords, search for both
- If only cause keywords, search only for caused deaths (C)
- If only present keywords, search only for present cases (P)
- If neither, search for both as default

### **Finding Demographics (Lines 308-340)**

**Lines 309-320:** Look for demographic keywords
- Check if the question mentions age groups, sex, race, or counties
- Store what we find in the demographics section

**Lines 322-340:** Look for specific demographic values
- If someone mentions "male", "female", "white", "black", etc.
- Store these specific values

### **Determining What Type of Analysis (Lines 342-413)**

This section figures out what kind of chart or analysis the user wants.

**Lines 342-344:** Pie charts
- If the question contains "pie chart" or "pie", they want a pie chart

**Lines 345-347:** Bar charts
- If the question contains "bar chart" or "bar", they want a bar chart

**Lines 348-350:** Line charts
- If the question contains "line chart", "line", or "trend", they want a line chart

**Lines 351-353:** Heatmaps
- If the question contains "heatmap" or "heat map", they want a heatmap

**Lines 354-356:** Scatter plots
- If the question contains "scatter" or "scatterplot", they want a scatter plot

**Lines 357-359:** Maps
- If the question contains "map", they want a geographic map

**Lines 360-395:** Correlation analysis
- If the question contains "correlation" or "corr", they want correlation analysis
- This section also does extra work to find ALL drugs mentioned in correlation questions
- Because correlation needs at least 2 things to compare

**Lines 397-399:** Drug summary table
- If the question contains "generate" and "table", they want a summary table

**Lines 401-411:** Other specific analyses
- Most deaths by county, least deaths, most deaths by year, etc.
- Each has specific keywords to look for

**Line 413:** Default to counting
- If we can't figure out what they want, just count the results

### **Finding Grouping Variables (Lines 415-430)**

**Lines 416-425:** Create a dictionary of grouping keywords
- This maps words like "county" to actual column names like "county name"
- Like a translation dictionary

**Lines 427-430:** Find what to group by
- Look through the question for grouping keywords
- Store what we find for making charts

**Line 432:** `return query`
- Give back all the information we figured out

---

## **PART 11: APPLYING FILTERS TO DATA (Lines 434-459)**

This function takes our data and filters it based on what the user asked for.

**Line 434:** `def apply_filters(df_input, query):`
- Start a function that filters the data

**Line 436:** `dff = df_input.copy()`
- Make a copy of the data so we don't change the original

### **Filter by Year (Lines 439-443)**

**Lines 439-443:** Apply year filters
- If the user asked for specific years, only keep data from those years
- Like keeping only 2022 data if they asked about 2022

### **Filter by Drugs (Lines 446-459)**

**Lines 446-459:** Apply drug filters
- If the user asked about specific drugs, only keep records with those drugs
- For multiple drugs, use OR logic (keep if ANY of the drugs match)
- Like keeping records that have either fentanyl OR cocaine

---

### **Filter by Demographics (Lines 461-480)**

**Lines 461-480:** Apply demographic filters
- If the user asked about specific age groups, sex, race, or counties
- Only keep records that match those criteria
- Like keeping only records of males if they asked about male deaths

**Line 482:** `return dff`
- Give back the filtered data

---

## **PART 12: VISUALIZATION FUNCTIONS (Lines 484-864)**

These functions create different types of charts and graphs.

### **Pie Chart Function (Lines 484-520)**

**Line 484:** `def create_pie_chart(dff, query):`
- Start a function to make pie charts

**Lines 486-490:** Check if we have data and grouping
- Make sure there's data to chart and we know what to group by

**Lines 492-520:** Create and display the pie chart
- Count how many records are in each group
- Use Plotly to make an interactive pie chart
- Display it on the webpage

### **Bar Chart Function (Lines 522-558)**

**Line 522:** `def create_bar_chart(dff, query):`
- Start a function to make bar charts

**Lines 524-558:** Similar to pie chart but makes bars instead
- Count records in each group
- Create a bar chart showing the counts
- Display it with colors and labels

### **Line Chart Function (Lines 560-596)**

**Line 560:** `def create_line_chart(dff, query):`
- Start a function to make line charts (usually for trends over time)

**Lines 562-596:** Create line chart
- Usually groups data by year to show trends
- Makes a line that goes up and down over time
- Good for seeing if something is increasing or decreasing

### **Heatmap Function (Lines 598-634)**

**Line 598:** `def create_heatmap(dff, query):`
- Start a function to make heatmaps (like a colorful grid)

**Lines 600-634:** Create heatmap
- Takes two grouping variables (like age and sex)
- Creates a grid where colors show how many cases are in each combination
- Darker colors mean more cases

### **Scatter Plot Function (Lines 636-672)**

**Line 636:** `def create_scatter_plot(dff, query):`
- Start a function to make scatter plots (dots on a graph)

**Lines 638-672:** Create scatter plot
- Puts dots on a graph where each dot represents one person
- Good for seeing relationships between two number variables
- Like age vs number of substances

### **Map Function (Lines 674-710)**

**Line 674:** `def create_map(dff, query):`
- Start a function to make geographic maps

**Lines 676-710:** Create map
- Colors different counties based on how many cases they have
- Darker colors mean more cases in that county
- Uses the Florida county map data we loaded earlier

---

## **PART 13: CORRELATION ANALYSIS FUNCTION (Lines 712-800)**

This function finds relationships between different variables.

**Line 712:** `def analyze_correlation(dff, query):`
- Start a function to analyze correlations (relationships)

**Line 715:** `variables = []`
- Create an empty list to store the variables we want to compare

### **Finding Variables to Correlate (Lines 718-760)**

**Lines 718-730:** Find drugs mentioned in correlation queries
- Look through all drug names to see which ones appear in the question
- Also check drug aliases and use fuzzy matching

**Lines 732-750:** Add drug variables
- For each drug found, create a binary variable (0 or 1)
- 1 means the drug was present, 0 means it wasn't
- This lets us do math with drug presence

**Lines 752-760:** Add demographic variables
- Look for mentions of age, substance counts, etc.
- Add these as variables to correlate

### **Special Handling (Lines 762-775)**

**Lines 762-770:** Handle common correlation patterns
- If someone asks about "age and fentanyl", make sure we include age
- If someone asks about substance counts, include those

**Lines 772-775:** Check if we have enough variables
- Correlation needs at least 2 variables to compare
- If we don't have enough, show an error message

### **Calculate and Display Correlation (Lines 777-800)**

**Lines 777-800:** Do the actual correlation analysis
- Create a data table with all the variables
- Calculate correlation coefficients (numbers showing how related things are)
- Create visualizations showing the relationships
- Display statistical significance (how confident we are in the results)

---

## **PART 14: DRUG SUMMARY TABLE FUNCTION (Lines 802-906)**

This function creates comprehensive tables showing all drugs.

**Line 802:** `def generate_drug_summary_table(dff, query):`
- Start a function to make drug summary tables

**Line 805:** `drug_summary = []`
- Create an empty list to store information about each drug

### **Collecting Drug Information (Lines 807-820)**

**Lines 807-820:** Go through each drug
- For each drug in our dataset:
  - Count how many deaths it CAUSED (C)
  - Count how many cases it was PRESENT in (P)
  - Only include drugs that have at least some data

### **Creating and Formatting the Table (Lines 822-906)**

**Lines 822-830:** Convert to a proper table format
- Turn our list into a pandas DataFrame (like an Excel table)
- Sort by total cases so the most common drugs appear first

**Lines 832-845:** Create a descriptive title
- Based on what filters the user applied
- Like "Drug Overdose Summary Table for 2022 in Miami-Dade"

**Lines 847-860:** Display the interactive table
- Show the table on the webpage with nice formatting
- Make columns sortable and searchable

**Lines 862-870:** Show summary statistics
- Display total numbers at the bottom
- Show metrics like total drugs, total deaths, etc.

**Lines 872-885:** Show top 10 drugs
- List the 10 most common drugs with details
- Show percentages for caused vs present

**Lines 887-906:** Add download option
- Let users download the table as a CSV file
- Good for further analysis in Excel

---

## **PART 15: MAIN QUERY HANDLER (Lines 908-1020)**

This is the "traffic director" that decides what to do with each query.

**Line 908:** `def handle_query(query):`
- Start the main function that handles all user questions

### **Apply Filters and Check Data (Lines 911-950)**

**Line 911:** `dff = apply_filters(df, query)`
- Filter the data based on what the user asked for

**Lines 913-950:** Check if we have data
- If no data matches the query, show a helpful error message
- Include suggestions for what might be wrong
- Show available drugs and years if the query failed

### **Route to Appropriate Function (Lines 952-1020)**

**Lines 952-1020:** Decide what to do based on the intent
- If they want a pie chart, call the pie chart function
- If they want correlation, call the correlation function
- If they want a table, call the table function
- etc.

This is like a switchboard operator directing calls to the right department.

### **Default Counting (Lines 1010-1020)**

**Lines 1010-1020:** Handle simple counting queries
- If someone just wants to know "how many", count the results
- But first check if they provided meaningful filters
- Don't just return the total number of records for nonsense queries

---

## **PART 16: MAIN EXECUTION (Lines 1022-1089)**

This is where everything comes together and the app actually runs.

**Line 1022:** `if user_input:`
- Only do something if the user typed a question

### **Validate the Query (Lines 1024-1035)**

**Lines 1024-1035:** Check if the question makes sense
- Use our validation function to check for nonsense
- If invalid, show error message and helpful examples
- Don't waste time processing gibberish

### **Process Valid Queries (Lines 1036-1089)**

**Line 1037:** `query = parse_query(user_input)`
- If the question is valid, figure out what they want

**Lines 1040-1055:** Show debug information
- Create an expandable section showing how we interpreted the query
- Show what drugs we found, what intent we detected, etc.
- This helps users understand what the app is thinking

**Lines 1057-1070:** Show fuzzy matching
- If we corrected any spelling mistakes, tell the user
- Like showing "fentanil → fentanyl"

**Lines 1072-1075:** Execute the query
- Call our main handler function to actually answer the question
- Show the results to the user

---

## **SUMMARY: HOW IT ALL WORKS TOGETHER**

1. **User types a question** in the text box
2. **Validation function** checks if it makes sense
3. **Parser function** figures out what they want (drugs, years, chart type, etc.)
4. **Filter function** narrows down the data to match their request
5. **Handler function** decides which visualization or analysis to do
6. **Specific function** (pie chart, correlation, etc.) creates the result
7. **Results are displayed** on the webpage

The app is like a smart assistant that:
- Understands natural language questions
- Corrects spelling mistakes
- Rejects nonsense input
- Creates appropriate visualizations
- Provides helpful error messages

Every line of code serves a purpose in this process, from the simple imports at the top to the complex analysis functions in the middle to the user interface at the bottom.
