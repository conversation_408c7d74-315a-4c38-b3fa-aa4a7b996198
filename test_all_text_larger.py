#!/usr/bin/env python3
"""
Test that ALL text in the Site Information tab is now larger
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING ALL TEXT IN SITE INFORMATION TAB IS LARGER")
    print("=" * 80)
    
    # Test 1: Check comprehensive expandable section styling
    print("\n📝 Test 1: Expandable Sections (Drug Statistics, Query Limitations)")
    print("-" * 70)
    
    expandable_checks = [
        ('.stExpander .stMarkdown p', 'Expandable paragraph text sized to 22px'),
        ('.stExpander .stMarkdown li', 'Expandable list items sized to 22px'),
        ('.stExpander .stMarkdown h1', 'Expandable H1 headers sized to 32px'),
        ('.stExpander .stMarkdown h2', 'Expandable H2 headers sized to 28px'),
        ('.stExpander .stMarkdown h3', 'Expandable H3 headers sized to 26px'),
        ('.stExpander .stMarkdown h4', 'Expandable H4 headers sized to 24px'),
        ('.stExpander .stMarkdown strong', 'Expandable bold text sized to 22px'),
        ('.stExpander .stMarkdown code', 'Expandable code text sized to 20px'),
        ('line-height: 1.7 !important', 'Improved line spacing in expandables')
    ]
    
    for check, description in expandable_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 2: Check comprehensive CSS overrides
    print("\n📝 Test 2: Comprehensive CSS Overrides for ALL Elements")
    print("-" * 70)
    
    override_checks = [
        ('.about-section *', 'Universal selector for all elements'),
        ('.stMarkdown, .about-section .stMarkdown *', 'All markdown content'),
        ('div, .about-section span, .about-section p', 'All basic HTML elements'),
        ('.about-section [class*="css-"]', 'All Streamlit CSS classes'),
        ('.stDataFrame, .about-section .stDataFrame *', 'All dataframe content'),
        ('table, .about-section table *', 'All table elements'),
        ('font-size: 22px !important', 'Base 22px font size applied')
    ]
    
    for check, description in override_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 3: Check specific section headers converted to markdown
    print("\n📝 Test 3: Section Headers Converted to Markdown (for consistent styling)")
    print("-" * 70)
    
    header_checks = [
        ('st.markdown("### 📈 Drug Usage Statistics")', 'Drug Usage Statistics header'),
        ('st.markdown("**Top 15 Most Common Drugs:**")', 'Top drugs subheader'),
        ('st.markdown("### 📈 County Statistics")', 'County Statistics header'),
        ('st.markdown("**Top 15 Counties by Total Cases:**")', 'Top counties subheader'),
        ('st.markdown("### 📈 Cases by Year")', 'Cases by Year header')
    ]
    
    for check, description in header_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 4: Check individual element styling
    print("\n📝 Test 4: Individual Element Styling (Drug Names, County Names)")
    print("-" * 70)
    
    element_checks = [
        ("font-size: 22px; margin: 8px 0", 'Individual drug/county name styling'),
        ("font-size: 22px; margin: 12px 0; font-weight: 500", 'Years display styling'),
        ('st.markdown(f"<p style=', 'HTML paragraph styling for lists')
    ]
    
    for check, description in element_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 5: Count total CSS rules for font sizing
    print("\n📝 Test 5: CSS Rule Coverage")
    print("-" * 70)
    
    font_size_rules = len(re.findall(r'font-size:\s*\d+px', content))
    important_rules = len(re.findall(r'!important', content))
    css_selectors = len(re.findall(r'\.about-section[^{]*{', content))
    
    print(f"✅ Font-size rules: {font_size_rules}")
    print(f"✅ !important declarations: {important_rules}")
    print(f"✅ CSS selectors targeting about-section: {css_selectors}")
    
    if font_size_rules >= 30:
        print(f"✅ Comprehensive font sizing (30+ rules)")
    else:
        print(f"⚠️  Limited font sizing ({font_size_rules} rules)")
    
    # Test 6: Check for specific problem areas that might have been missed
    print("\n📝 Test 6: Specific Content Areas")
    print("-" * 70)
    
    content_checks = [
        ('Query Limitations & Inaccurate Results', 'Query limitations section exists'),
        ('Drug Usage Statistics', 'Drug usage statistics section exists'),
        ('stExpander', 'Expandable sections present'),
        ('stDataFrame', 'Data tables present'),
        ('stMetric', 'Metrics present')
    ]
    
    for check, description in content_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    print(f"\n🎉 ALL TEXT SIZING TEST COMPLETED!")
    print("=" * 80)
    
    # Summary of comprehensive coverage
    coverage_areas = []
    if '.stExpander .stMarkdown p' in content:
        coverage_areas.append("✅ Expandable section content")
    if '.about-section *' in content:
        coverage_areas.append("✅ Universal element coverage")
    if 'st.markdown("### 📈 Drug Usage Statistics")' in content:
        coverage_areas.append("✅ Section headers converted")
    if "font-size: 22px; margin: 8px 0" in content:
        coverage_areas.append("✅ Individual element styling")
    if font_size_rules >= 30:
        coverage_areas.append("✅ Comprehensive CSS rules")
    
    print(f"\n📋 Coverage Summary:")
    for area in coverage_areas:
        print(f"   {area}")
    
    if len(coverage_areas) >= 4:
        print(f"\n🎉 COMPREHENSIVE TEXT SIZING SUCCESSFULLY IMPLEMENTED!")
        print(f"   📏 ALL expandable content: 22px+ fonts")
        print(f"   📏 Drug Usage Statistics: Large fonts throughout")
        print(f"   📏 Query Limitations: Large fonts throughout")
        print(f"   📏 All tables and data: 20-22px fonts")
        print(f"   📏 All headers and text: Consistently large")
        print(f"   📏 Universal CSS coverage: Every element targeted")
    else:
        print(f"\n⚠️  Some areas may need additional coverage")
    
    print(f"\n🚀 Ready to test COMPREHENSIVE large fonts!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Check: Site Information tab")
    print(f"   Verify: ALL text should be noticeably larger, including:")
    print(f"   - Drug names in lists")
    print(f"   - Drug Usage Statistics content")
    print(f"   - Query Limitations expandable content")
    print(f"   - All tables and data")
    print(f"   - All headers and subheaders")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
