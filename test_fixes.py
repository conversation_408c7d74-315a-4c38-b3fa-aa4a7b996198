#!/usr/bin/env python3
"""
Test script to verify the fixes for the enhanced drug query chatbot
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    # Import the main functions from our enhanced chatbot
    from drug_query_chatbot_app import parse_query, apply_filters, df, drug_columns
    
    print("✅ Successfully imported enhanced chatbot functions")
    print(f"📊 Data loaded: {len(df):,} records")
    print(f"💊 Drug columns: {len(drug_columns)}")
    
    # Test the specific problematic queries
    print("\n🧪 Testing Problematic Queries:")
    
    # Test 1: "Deaths caused by fentanyl in 2022"
    print("\n📝 Test 1: 'Deaths caused by fentanyl in 2022'")
    query1 = parse_query("Deaths caused by fentanyl in 2022")
    print(f"   Parsed drugs: {query1.get('drugs')}")
    print(f"   Parsed status: {query1.get('status')}")
    print(f"   Parsed year: {query1.get('year')}")
    print(f"   Intent: {query1.get('intent')}")
    
    # Apply filters
    filtered1 = apply_filters(df, query1)
    print(f"   Filtered results: {len(filtered1):,} records")
    
    # Test 2: "correlation between age and fentanyl"
    print("\n📝 Test 2: 'correlation between age and fentanyl'")
    query2 = parse_query("correlation between age and fentanyl")
    print(f"   Parsed drugs: {query2.get('drugs')}")
    print(f"   Intent: {query2.get('intent')}")
    print(f"   Original text: {query2.get('original_text')}")
    
    # Test 3: Check if fentanyl exists in drug columns
    print("\n📝 Test 3: Drug column verification")
    fentanyl_cols = [col for col in drug_columns if 'fentanyl' in col.lower()]
    print(f"   Fentanyl-related columns: {fentanyl_cols}")
    
    if fentanyl_cols:
        fentanyl_col = fentanyl_cols[0]
        print(f"   Using column: {fentanyl_col}")
        
        # Check values in fentanyl column
        fentanyl_values = df[fentanyl_col].value_counts()
        print(f"   Values in {fentanyl_col}: {fentanyl_values.to_dict()}")
        
        # Test filtering with actual column
        test_filter = df[df[fentanyl_col].isin(['C', 'P'])]
        print(f"   Records with fentanyl C or P: {len(test_filter):,}")
        
        # Test with year filter
        if 'year' in df.columns:
            test_filter_year = test_filter[test_filter['year'] == 2022]
            print(f"   Records with fentanyl in 2022: {len(test_filter_year):,}")
    
    # Test 4: Alternative drug names
    print("\n📝 Test 4: Alternative drug name queries")
    alt_queries = [
        "Deaths caused by cocaine in 2022",
        "Deaths caused by heroin in 2021", 
        "Deaths caused by meth in 2023"
    ]
    
    for alt_query in alt_queries:
        parsed = parse_query(alt_query)
        filtered = apply_filters(df, parsed)
        print(f"   '{alt_query}': {len(filtered):,} records")
    
    print("\n🎉 Testing completed!")
    
    # Show some helpful information
    print("\n📋 Helpful Information:")
    print(f"   Available years: {sorted(df['year'].dropna().unique())}")
    print(f"   Sample drug columns: {sorted(drug_columns)[:10]}")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
