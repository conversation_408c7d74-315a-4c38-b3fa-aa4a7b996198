#!/usr/bin/env python3
"""
Test that the tab fonts have been reduced to a more reasonable size
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING REDUCED TAB FONT SIZES")
    print("=" * 45)
    
    # Test 1: Check reduced font sizes
    print("\n📝 Test 1: Reduced Font Sizes")
    print("-" * 35)
    
    font_size_checks = [
        ('font-size: 18px !important', 'Tab text font size reduced to 18px'),
        ('font-weight: 700 !important', 'Tab text font weight reduced to 700'),
        ('font-size: 36px', 'Old 36px font size should be gone'),
        ('font-weight: 900', 'Old 900 font weight should be gone')
    ]
    
    font_18px = content.count('font-size: 18px')
    font_36px = content.count('font-size: 36px')
    weight_700 = content.count('font-weight: 700')
    weight_900 = content.count('font-weight: 900')
    
    print(f"✅ 18px font declarations: {font_18px}")
    print(f"✅ 36px font declarations: {font_36px}")
    print(f"✅ Font weight 700 declarations: {weight_700}")
    print(f"✅ Font weight 900 declarations: {weight_900}")
    
    if font_18px >= 5:
        print("✅ Tab text now uses 18px fonts (reasonable size)")
    else:
        print(f"⚠️  Tab text may not be consistently 18px ({font_18px} declarations)")
    
    if font_36px == 0:
        print("✅ Old 36px fonts completely removed")
    else:
        print(f"⚠️  Some 36px fonts may still exist ({font_36px} found)")
    
    # Test 2: Check font size progression
    print("\n📝 Test 2: Font Size Progression")
    print("-" * 35)
    
    original_size = 14  # Approximate original default
    previous_size = 36  # The too-large size
    current_size = 18   # New reasonable size
    
    reduction_from_large = ((previous_size - current_size) / previous_size) * 100
    increase_from_original = ((current_size - original_size) / original_size) * 100
    
    print(f"📊 Font Size Journey:")
    print(f"   Original: ~{original_size}px")
    print(f"   Too large: {previous_size}px")
    print(f"   Current: {current_size}px")
    print(f"   Reduction from large: -{reduction_from_large:.0f}%")
    print(f"   Increase from original: +{increase_from_original:.0f}%")
    
    if reduction_from_large >= 45:
        print(f"✅ Significant reduction achieved (-{reduction_from_large:.0f}%)")
    
    # Test 3: Check weight reduction
    print("\n📝 Test 3: Font Weight Reduction")
    print("-" * 35)
    
    if weight_700 >= 3 and weight_900 == 0:
        print("✅ Font weight reduced from 900 to 700 (still bold but not extreme)")
    elif weight_900 > 0:
        print(f"⚠️  Some 900 weight fonts may still exist ({weight_900} found)")
    else:
        print("✅ Font weight successfully moderated")
    
    # Test 4: Check CSS targeting maintained
    print("\n📝 Test 4: CSS Targeting Maintained")
    print("-" * 35)
    
    targeting_checks = [
        ('[data-baseweb="tab"] button', 'Direct tab button targeting maintained'),
        ('[data-baseweb="tab"] button *', 'Universal selector within buttons maintained'),
        ('[data-baseweb="tab-list"] button', 'Tab list button targeting maintained'),
        ('MODERATE FONT SIZE TARGETING', 'CSS comment updated to reflect moderate sizing')
    ]
    
    for check, description in targeting_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    print(f"\n🎉 REDUCED TAB FONT SIZE TEST COMPLETED!")
    print("=" * 45)
    
    # Summary assessment
    reduction_score = 0
    if font_18px >= 5:
        reduction_score += 1
    if font_36px == 0:
        reduction_score += 1
    if weight_700 >= 3 and weight_900 == 0:
        reduction_score += 1
    if '[data-baseweb="tab"] button' in content:
        reduction_score += 1
    
    print(f"\n📋 Assessment Summary:")
    print(f"   Reduction score: {reduction_score}/4")
    print(f"   18px font declarations: {font_18px}")
    print(f"   36px font declarations: {font_36px}")
    print(f"   Font weight 700 declarations: {weight_700}")
    print(f"   Font weight 900 declarations: {weight_900}")
    
    if reduction_score >= 3 and font_18px >= 5:
        print(f"\n🎉 SUCCESS: TAB FONTS REDUCED TO REASONABLE SIZE!")
        print(f"   📏 Font size: 36px → 18px (50% reduction)")
        print(f"   📏 Font weight: 900 → 700 (more moderate)")
        print(f"   📏 Still larger than original: +{increase_from_original:.0f}% vs default")
        print(f"   📏 Much more readable: Not overwhelming")
        print(f"   📏 Professional appearance: Balanced sizing")
        print(f"   📏 CSS targeting: All selectors maintained")
    else:
        print(f"\n⚠️  Some font reduction may be incomplete")
        if font_18px < 5:
            print(f"   - Need more 18px font declarations")
        if font_36px > 0:
            print(f"   - Some 36px fonts may still exist")
        if weight_900 > 0:
            print(f"   - Some 900 weight fonts may still exist")
    
    print(f"\n🚀 Ready to see reasonably-sized tab text!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Notice: 18px text (half of the previous 36px)")
    print(f"   See: Still bold (700) but not overwhelming")
    print(f"   Feel: Much more comfortable and readable")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
