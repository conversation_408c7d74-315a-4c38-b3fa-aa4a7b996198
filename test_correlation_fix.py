#!/usr/bin/env python3
"""
Test the specific correlation issue: "correlation between deaths caused by fentanyl and deaths caused by cocaine"
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import parse_query, analyze_correlation, apply_filters, df, drug_columns
    
    print("✅ Testing correlation fix")
    print(f"📊 Data loaded: {len(df):,} records")
    
    # Test the specific problematic query
    query_text = "correlation between deaths caused by fentanyl and deaths caused by cocaine"
    print(f"\n📝 Testing: '{query_text}'")
    
    # Parse the query
    query = parse_query(query_text)
    print(f"   Intent: {query.get('intent')}")
    print(f"   Drugs found: {query.get('drugs')}")
    print(f"   Status: {query.get('status')}")
    print(f"   Original text: {query.get('original_text')}")
    
    # Apply filters
    dff = apply_filters(df, query)
    print(f"   Filtered data: {len(dff):,} records")
    
    # Test if both drugs exist in the dataset
    fentanyl_exists = 'fentanyl' in df.columns
    cocaine_exists = 'cocaine' in df.columns
    print(f"   Fentanyl column exists: {fentanyl_exists}")
    print(f"   Cocaine column exists: {cocaine_exists}")
    
    if fentanyl_exists and cocaine_exists:
        # Check data availability
        fentanyl_data = df['fentanyl'].value_counts()
        cocaine_data = df['cocaine'].value_counts()
        print(f"   Fentanyl data: {fentanyl_data.to_dict()}")
        print(f"   Cocaine data: {cocaine_data.to_dict()}")
        
        # Test correlation analysis manually
        print(f"\n🔗 Testing correlation analysis...")
        
        # Create binary variables for both drugs
        fentanyl_binary = (df['fentanyl'].isin(['C', 'P'])).astype(int)
        cocaine_binary = (df['cocaine'].isin(['C', 'P'])).astype(int)
        
        print(f"   Fentanyl binary: {fentanyl_binary.sum():,} positive cases")
        print(f"   Cocaine binary: {cocaine_binary.sum():,} positive cases")
        
        # Calculate correlation
        correlation_data = pd.DataFrame({
            'fentanyl': fentanyl_binary,
            'cocaine': cocaine_binary
        }).dropna()
        
        if len(correlation_data) > 0:
            corr_matrix = correlation_data.corr()
            correlation_value = corr_matrix.loc['fentanyl', 'cocaine']
            print(f"   Correlation coefficient: {correlation_value:.4f}")
            
            # Test the actual function
            print(f"\n🧪 Testing analyze_correlation function...")
            try:
                result = analyze_correlation(dff, query)
                if isinstance(result, str) and "❌" in result:
                    print(f"   Function returned error: {result}")
                else:
                    print(f"   Function should work (returned: {type(result)})")
            except Exception as e:
                print(f"   Function error: {e}")
        else:
            print(f"   No data available for correlation")
    
    # Test other correlation queries
    print(f"\n📝 Testing other correlation queries:")
    
    test_queries = [
        "correlation between fentanyl and cocaine",
        "correlation between age and fentanyl", 
        "correlation between heroin and methamphetamine"
    ]
    
    for test_query in test_queries:
        parsed = parse_query(test_query)
        print(f"   '{test_query}': drugs={parsed.get('drugs')}, intent={parsed.get('intent')}")
    
    print(f"\n🎉 Correlation testing completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
