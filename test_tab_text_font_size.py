#!/usr/bin/env python3
"""
Test that the font size of the text INSIDE the tabs is much larger
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING TAB TEXT FONT SIZE INCREASES")
    print("=" * 55)
    
    # Test 1: Check aggressive font size targeting for tab text
    print("\n📝 Test 1: Aggressive Font Size Targeting")
    print("-" * 45)
    
    text_targeting_checks = [
        ('font-size: 36px !important', 'Tab text font size set to 36px'),
        ('font-weight: 900 !important', 'Tab text font weight set to 900 (heaviest)'),
        ('.stTabs [data-baseweb="tab"] button {', 'Direct button targeting'),
        ('.stTabs [data-baseweb="tab"] button *', 'All elements within button targeted'),
        ('.stTabs [data-baseweb="tab"] button span', 'Span elements within button targeted'),
        ('.stTabs [data-baseweb="tab"] button div', 'Div elements within button targeted'),
        ('.stTabs [data-baseweb="tab-list"] button', 'Tab list button targeting'),
        ('.stTabs [data-baseweb="tab-list"] button *', 'All elements within tab list button')
    ]
    
    for check, description in text_targeting_checks:
        count = content.count(check)
        if count > 0:
            print(f"✅ {description} (found {count} times)")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 2: Check font size progression
    print("\n📝 Test 2: Font Size Progression")
    print("-" * 45)
    
    # Count different font sizes
    font_36px = content.count('font-size: 36px')
    font_32px = content.count('font-size: 32px')
    font_24px = content.count('font-size: 24px')
    font_20px = content.count('font-size: 20px')
    
    print(f"✅ 36px font declarations: {font_36px}")
    print(f"✅ 32px font declarations: {font_32px}")
    print(f"✅ 24px font declarations: {font_24px}")
    print(f"✅ 20px font declarations: {font_20px}")
    
    if font_36px >= 5:
        print("✅ Tab text now uses 36px fonts (multiple declarations)")
    else:
        print(f"⚠️  Tab text may not be consistently 36px ({font_36px} declarations)")
    
    # Test 3: Check font weight progression
    print("\n📝 Test 3: Font Weight Progression")
    print("-" * 45)
    
    weight_900 = content.count('font-weight: 900')
    weight_800 = content.count('font-weight: 800')
    weight_700 = content.count('font-weight: 700')
    
    print(f"✅ Font weight 900 declarations: {weight_900}")
    print(f"✅ Font weight 800 declarations: {weight_800}")
    print(f"✅ Font weight 700 declarations: {weight_700}")
    
    if weight_900 >= 3:
        print("✅ Tab text now uses heaviest font weight (900)")
    else:
        print(f"⚠️  Tab text may not be using heaviest weight ({weight_900} declarations)")
    
    # Test 4: Check comprehensive targeting
    print("\n📝 Test 4: Comprehensive CSS Targeting")
    print("-" * 45)
    
    targeting_checks = [
        ('[data-baseweb="tab"] button', 'Direct tab button targeting'),
        ('[data-baseweb="tab"] button *', 'Universal selector within buttons'),
        ('[data-baseweb="tab-list"] button', 'Tab list button targeting'),
        ('[class*="css-"]', 'Streamlit CSS class override'),
        ('line-height: 1.2 !important', 'Line height optimization for large text')
    ]
    
    for check, description in targeting_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 5: Calculate font size increase
    print("\n📝 Test 5: Font Size Increase Calculation")
    print("-" * 45)
    
    original_size = 20  # Original default tab font size
    current_size = 36   # New tab font size
    
    increase_percentage = ((current_size - original_size) / original_size) * 100
    
    print(f"📊 Font Size Progression:")
    print(f"   Original: {original_size}px")
    print(f"   Current:  {current_size}px")
    print(f"   Increase: +{increase_percentage:.0f}%")
    
    if increase_percentage >= 75:
        print(f"✅ Massive font size increase achieved (+{increase_percentage:.0f}%)")
    else:
        print(f"⚠️  Font size increase may be insufficient (+{increase_percentage:.0f}%)")
    
    print(f"\n🎉 TAB TEXT FONT SIZE TEST COMPLETED!")
    print("=" * 55)
    
    # Summary assessment
    text_targeting_score = 0
    if font_36px >= 5:
        text_targeting_score += 1
    if weight_900 >= 3:
        text_targeting_score += 1
    if '[data-baseweb="tab"] button *' in content:
        text_targeting_score += 1
    if '[data-baseweb="tab-list"] button *' in content:
        text_targeting_score += 1
    
    print(f"\n📋 Assessment Summary:")
    print(f"   Text targeting score: {text_targeting_score}/4")
    print(f"   36px font declarations: {font_36px}")
    print(f"   Font weight 900 declarations: {weight_900}")
    print(f"   Font size increase: +{increase_percentage:.0f}%")
    
    if text_targeting_score >= 3 and font_36px >= 5:
        print(f"\n🎉 SUCCESS: TAB TEXT FONTS ARE NOW MUCH LARGER!")
        print(f"   📏 Text size: 20px → 36px (+80% increase)")
        print(f"   📏 Font weight: 600 → 900 (heaviest possible)")
        print(f"   📏 Multiple targeting: {font_36px} declarations of 36px")
        print(f"   📏 Comprehensive CSS: All text elements targeted")
        print(f"   📏 Override protection: Multiple selectors ensure consistency")
        print(f"   📏 Line height: Optimized for large text readability")
    else:
        print(f"\n⚠️  Some text targeting may be incomplete")
        if font_36px < 5:
            print(f"   - Need more 36px font declarations")
        if weight_900 < 3:
            print(f"   - Need more font weight 900 declarations")
        if text_targeting_score < 3:
            print(f"   - Need better CSS targeting")
    
    print(f"\n🚀 Ready to see MUCH LARGER tab text!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Look for: 36px text inside the tabs")
    print(f"   Notice: Much heavier font weight (900)")
    print(f"   See: Dramatically more readable tab labels")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
