#!/usr/bin/env python3
"""
Test that validation now works for ALL drugs, not just the first 20
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import validate_query, drug_columns
    
    print("🧪 TESTING ALL DRUGS VALIDATION FIX")
    print("=" * 60)
    
    # Get all drugs sorted alphabetically
    all_drugs_sorted = sorted(drug_columns)
    
    print(f"📊 Dataset Information:")
    print(f"   Total drugs in dataset: {len(drug_columns)}")
    print(f"   First 10 drugs: {all_drugs_sorted[:10]}")
    print(f"   Last 10 drugs: {all_drugs_sorted[-10:]}")
    
    # Test drugs from different parts of the alphabet
    print(f"\n📝 Testing Validation for Drugs Throughout Alphabet:")
    print("-" * 60)
    
    # Test first 5 drugs (these should have always worked)
    print(f"\n✅ First 5 drugs (should always work):")
    for i, drug in enumerate(all_drugs_sorted[:5], 1):
        query = f"Deaths caused by {drug}"
        is_valid, error_msg = validate_query(query)
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"   {i}. '{drug}': {status}")
    
    # Test drugs 21-25 (these might have failed before the fix)
    print(f"\n🔍 Drugs 21-25 (might have failed before fix):")
    for i, drug in enumerate(all_drugs_sorted[20:25], 21):
        query = f"Deaths caused by {drug}"
        is_valid, error_msg = validate_query(query)
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"   {i}. '{drug}': {status}")
        if not is_valid:
            print(f"      Error: {error_msg.split('.')[0]}...")
    
    # Test drugs 41-45 (these definitely would have failed before)
    print(f"\n🔍 Drugs 41-45 (would have failed before fix):")
    for i, drug in enumerate(all_drugs_sorted[40:45], 41):
        query = f"Deaths caused by {drug}"
        is_valid, error_msg = validate_query(query)
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"   {i}. '{drug}': {status}")
        if not is_valid:
            print(f"      Error: {error_msg.split('.')[0]}...")
    
    # Test last 5 drugs (these definitely would have failed before)
    print(f"\n🔍 Last 5 drugs (would have failed before fix):")
    for i, drug in enumerate(all_drugs_sorted[-5:], len(all_drugs_sorted)-4):
        query = f"Deaths caused by {drug}"
        is_valid, error_msg = validate_query(query)
        status = "✅ Valid" if is_valid else "❌ Invalid"
        print(f"   {i}. '{drug}': {status}")
        if not is_valid:
            print(f"      Error: {error_msg.split('.')[0]}...")
    
    # Test some specific drugs that might be later in the alphabet
    print(f"\n🧪 Testing Specific Later-Alphabet Drugs:")
    print("-" * 50)
    
    test_drugs = []
    # Find some drugs that start with letters later in alphabet
    for drug in all_drugs_sorted:
        if drug.lower().startswith(('t', 'u', 'v', 'w', 'x', 'y', 'z')):
            test_drugs.append(drug)
            if len(test_drugs) >= 5:
                break
    
    if test_drugs:
        for drug in test_drugs:
            query = f"How many {drug} deaths in 2022?"
            is_valid, error_msg = validate_query(query)
            status = "✅ Valid" if is_valid else "❌ Invalid"
            print(f"   '{drug}': {status}")
    else:
        print("   No drugs found starting with later alphabet letters")
    
    # Count how many drugs would pass validation
    print(f"\n📈 Validation Success Rate:")
    print("-" * 30)
    
    valid_count = 0
    invalid_count = 0
    
    for drug in all_drugs_sorted:
        query = f"Deaths caused by {drug}"
        is_valid, _ = validate_query(query)
        if is_valid:
            valid_count += 1
        else:
            invalid_count += 1
    
    total_drugs = len(all_drugs_sorted)
    success_rate = (valid_count / total_drugs) * 100
    
    print(f"   Total drugs tested: {total_drugs}")
    print(f"   Valid queries: {valid_count}")
    print(f"   Invalid queries: {invalid_count}")
    print(f"   Success rate: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print(f"   🎉 Excellent! Almost all drugs pass validation")
    elif success_rate >= 80:
        print(f"   ✅ Good! Most drugs pass validation")
    else:
        print(f"   ⚠️  Some drugs still failing validation")
    
    print(f"\n🎉 ALL DRUGS VALIDATION TEST COMPLETED!")
    print("=" * 60)
    
    if invalid_count == 0:
        print("✅ SUCCESS: All drugs now pass validation!")
        print("✅ Users can ask about any drug in the dataset")
        print("✅ No more arbitrary 20-drug limit")
    else:
        print(f"⚠️  {invalid_count} drugs still fail validation")
        print("   This might be due to very short drug names or special characters")
    
    print(f"\n💡 The fix ensures that queries like:")
    print(f"   - 'Deaths caused by {all_drugs_sorted[-1]}' (last drug alphabetically)")
    print(f"   - 'How many {all_drugs_sorted[30]} overdoses?' (middle of list)")
    print(f"   - '{all_drugs_sorted[50]} deaths by race' (later in list)")
    print(f"   All work properly now!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
