#!/usr/bin/env python3
"""
Test that the Query Limitations & Inaccurate Results section has larger fonts
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING QUERY LIMITATIONS SECTION FONT INCREASES")
    print("=" * 70)
    
    # Test 1: Check introduction text
    print("\n📝 Test 1: Introduction Text")
    print("-" * 50)
    
    intro_checks = [
        ('<div style="font-size: 24px; line-height: 1.6; margin-bottom: 20px;">', 'Introduction text with 24px font'),
        ('This section describes queries that will <strong>NOT work</strong>', 'Bold emphasis in introduction'),
        ('Understanding these limitations will help you', 'Large introduction text')
    ]
    
    for check, description in intro_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 2: Check aggressive CSS targeting
    print("\n📝 Test 2: Aggressive CSS Targeting for Expandables")
    print("-" * 50)
    
    css_checks = [
        ('.stExpander[data-testid="expander"] *', 'Universal expandable content targeting'),
        ('.stExpander[data-testid="expander"] div', 'Expandable div targeting'),
        ('.stExpander[data-testid="expander"] p', 'Expandable paragraph targeting'),
        ('.stExpander[data-testid="expander"] li', 'Expandable list item targeting'),
        ('.stExpander[data-testid="expander"] strong', 'Expandable bold text targeting'),
        ('.stExpander[data-testid="expander"] code', 'Expandable code targeting'),
        ('font-size: 24px !important', 'Base expandable font size 24px'),
        ('font-size: 28px !important', 'Expandable header font size 28px'),
        ('padding: 20px !important', 'Expandable header padding increased')
    ]
    
    for check, description in css_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 3: Check HTML styling in expandable content
    print("\n📝 Test 3: HTML Styling in Expandable Content")
    print("-" * 50)
    
    html_checks = [
        ('<div style="font-size: 24px; line-height: 1.6;">', 'HTML div with large fonts'),
        ('<h3 style="font-size: 28px; font-weight: bold;">', 'HTML headers with large fonts'),
        ('<p style="font-size: 24px; font-weight: bold;">❌', 'Bold section labels with large fonts'),
        ('<ul style="font-size: 24px; line-height: 1.6;">', 'HTML lists with large fonts'),
        ('<code style="font-size: 22px; background-color: #f0f0f0; padding: 2px 4px;">', 'Code snippets with large fonts'),
        ('unsafe_allow_html=True', 'HTML rendering enabled')
    ]
    
    for check, description in html_checks:
        count = content.count(check)
        if count > 0:
            print(f"✅ {description} (found {count} times)")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 4: Check specific content examples
    print("\n📝 Test 4: Specific Content Examples with Large Fonts")
    print("-" * 50)
    
    content_checks = [
        ('"Overdoses in California"', 'California example query'),
        ('"Drug deaths in 2025"', 'Future year example query'),
        ('"Overdoses from marijuana"', 'Marijuana example query'),
        ('"Deaths from aspirin"', 'Aspirin example query'),
        ('"Overdoses by income level"', 'Income level example query'),
        ('"Deaths of people with diabetes"', 'Diabetes example query'),
        ('"Deaths by zip code"', 'Zip code example query'),
        ('"Why do people overdose on fentanyl?"', 'Why question example')
    ]
    
    for check, description in content_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 5: Count font size implementations
    print("\n📝 Test 5: Font Size Implementation Count")
    print("-" * 50)
    
    # Count different font sizes used
    font_22px = content.count('font-size: 22px')
    font_24px = content.count('font-size: 24px')
    font_28px = content.count('font-size: 28px')
    html_styling = content.count('unsafe_allow_html=True')
    
    print(f"✅ 22px font declarations: {font_22px}")
    print(f"✅ 24px font declarations: {font_24px}")
    print(f"✅ 28px font declarations: {font_28px}")
    print(f"✅ HTML styling instances: {html_styling}")
    
    total_large_fonts = font_22px + font_24px + font_28px
    print(f"✅ Total large font declarations: {total_large_fonts}")
    
    print(f"\n🎉 QUERY LIMITATIONS FONT TEST COMPLETED!")
    print("=" * 70)
    
    # Summary of implementations
    implementations = []
    if '<div style="font-size: 24px; line-height: 1.6; margin-bottom: 20px;">' in content:
        implementations.append("✅ Introduction text: 24px fonts")
    if '.stExpander[data-testid="expander"] *' in content:
        implementations.append("✅ Aggressive CSS targeting: All expandable content")
    if '<h3 style="font-size: 28px; font-weight: bold;">' in content:
        implementations.append("✅ HTML headers: 28px fonts")
    if '<code style="font-size: 22px; background-color: #f0f0f0; padding: 2px 4px;">' in content:
        implementations.append("✅ Code examples: 22px fonts with styling")
    if total_large_fonts >= 30:
        implementations.append("✅ Comprehensive coverage: 30+ font declarations")
    
    print(f"\n📋 Implementation Summary:")
    for impl in implementations:
        print(f"   {impl}")
    
    if len(implementations) >= 4:
        print(f"\n🎉 QUERY LIMITATIONS SECTION SUCCESSFULLY UPDATED!")
        print(f"   📏 Introduction: 24px with proper spacing")
        print(f"   📏 Section headers: 28px bold")
        print(f"   📏 All content: 24px uniform text")
        print(f"   📏 Code examples: 22px with background styling")
        print(f"   📏 Lists: 24px with improved line spacing")
        print(f"   📏 CSS + HTML: Dual approach for guaranteed results")
        print(f"   📏 Total implementations: {total_large_fonts} font declarations")
    else:
        print(f"\n⚠️  Some areas may need additional updates")
    
    print(f"\n🚀 Ready to test Query Limitations section!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Check: Site Information tab")
    print(f"   Expand: '🚫 Queries That Will NOT Work' section")
    print(f"   Verify: ALL text should be much larger and readable")
    print(f"   Look for: Large headers, large bullet points, large code examples")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
