# FLORIDA DRUG OVERDOSE ANALYTICS APP
## Complete Code Explanation for Beginners

**Written for 7th Grade Understanding**
**Every Single Line Explained**
**Updated Version with Demographic Breakdown Feature**

---

## TABLE OF CONTENTS

1. [Importing Tools (Lines 1-16)](#part-1)
2. [Setting Up Colors and Page (Lines 18-21)](#part-2)
3. [Loading the Data (Lines 23-40)](#part-3)
4. [Loading Map Data (Lines 41-58)](#part-4)
5. [Organizing Our Data (Lines 60-86)](#part-5)
6. [Creating the Sidebar Help (Lines 82-151)](#part-6)
7. [Creating the Main Title (Lines 153-165)](#part-7)
8. [Fuzzy Matching Function (Lines 167-182)](#part-8)
9. [Query Validation Function (Lines 184-234)](#part-9)
10. [Main Query Parsing Function (Lines 236-430)](#part-10)
11. [Applying Filters to Data (Lines 451-476)](#part-11)
12. [Visualization Functions (Lines 501-881)](#part-12)
13. [Correlation Analysis Function (Lines 729-817)](#part-13)
14. [Drug Summary Table Function (Lines 819-923)](#part-14)
15. [**NEW: Demographic Breakdown Function (Lines 970-1224)**](#part-15)
16. [Main Query Handler (Lines 1225-1337)](#part-16)
17. [Main Execution (Lines 1339-1406)](#part-17)

---

## INTRODUCTION

This document explains every single line of code in the Florida Drug Overdose Analytics App. The app is like a smart assistant that can answer questions about drug overdose data in Florida. 

**What the app does:**
- Reads questions typed in plain English
- Understands what you want (charts, tables, statistics)
- Corrects spelling mistakes automatically
- Creates beautiful visualizations
- Provides detailed analysis
- **NEW: Breaks down drug overdoses by demographics (race, sex, age, county)**

**How to read this document:**
- Each section explains a different part of the code
- Line numbers refer to the actual code file
- Simple explanations use everyday analogies
- Technical terms are explained in plain language

---

## PART 1: IMPORTING TOOLS (Lines 1-16) {#part-1}

Think of importing like getting tools from a toolbox. Each tool helps our program do different things.

### Line 1: `import streamlit as st`
**What it does:** Gets a tool called Streamlit that helps us make websites and apps  
**Why we need it:** Streamlit makes it easy to create web pages with buttons, text boxes, and charts  
**The nickname:** We call it "st" for short, like calling "Michael" → "Mike"

### Line 2: `import pandas as pd`  
**What it does:** Gets a tool called Pandas that helps us work with data  
**Think of it like:** Excel for programmers - it handles spreadsheets and tables  
**The nickname:** We call it "pd" for short

### Line 3: `import matplotlib.pyplot as plt`
**What it does:** Gets a tool for making basic charts and graphs  
**Think of it like:** A basic drawing program for making simple charts  
**The nickname:** We call it "plt" for short

### Line 4: `import seaborn as sns`
**What it does:** Gets another tool for making prettier charts  
**Think of it like:** A fancy art program that makes matplotlib charts look better  
**The nickname:** We call it "sns" for short

### Line 5: `import re`
**What it does:** Gets a tool called "regular expressions" that helps us find patterns in text  
**Think of it like:** A super-powered "Find" function that can find complex patterns  
**Example:** Finding all phone numbers in a document, or all 4-digit years

### Line 6: `import plotly.express as px`
**What it does:** Gets a tool for making interactive charts (charts you can click on)  
**Think of it like:** Making charts that respond when you hover or click on them  
**The nickname:** We call it "px" for short

### Line 7: `import plotly.graph_objects as go`
**What it does:** Gets more advanced tools for making interactive charts  
**Think of it like:** The professional version of px with more options  
**The nickname:** We call it "go" for short

### Line 8: `import plotly.figure_factory as ff`
**What it does:** Gets tools for making special types of charts  
**Think of it like:** Pre-made templates for complex charts  
**The nickname:** We call it "ff" for short

### Line 9: `import os`
**What it does:** Gets a tool that helps us work with files and folders on the computer  
**Think of it like:** A file manager that can check if files exist, create folders, etc.

### Line 10: `import requests`
**What it does:** Gets a tool that helps us download things from the internet  
**Think of it like:** A web browser for programs - it can fetch web pages and files

### Line 11: `import json`
**What it does:** Gets a tool that helps us work with JSON data format  
**Think of it like:** A translator for a special way of organizing data that websites use

### Line 12: `import numpy as np`
**What it does:** Gets a tool that helps us do math with lots of numbers at once  
**Think of it like:** A super-calculator that can do math on thousands of numbers instantly  
**The nickname:** We call it "np" for short

### Line 13: `from scipy.stats import pearsonr, spearmanr, chi2_contingency`
**What it does:** Gets special math tools for statistics  
**What each does:**
- **pearsonr:** Finds if two things are related (like height and weight)
- **spearmanr:** Another way to find relationships  
- **chi2_contingency:** Tests if two categories are independent

### Line 14: `from difflib import get_close_matches`
**What it does:** Gets a tool that finds words that are similar to each other  
**Think of it like:** Spell-check that suggests corrections  
**Example:** Finding "fentanyl" when someone types "fentanil"

### Line 15: `import warnings`
**What it does:** Gets a tool that controls warning messages  
**Think of it like:** The volume control for error messages

### Line 16: `warnings.filterwarnings('ignore')`
**What it does:** Tells the program to hide warning messages  
**Why:** So they don't clutter our screen with unimportant messages

---

## PART 2: SETTING UP COLORS AND PAGE (Lines 18-21) {#part-2}

### Line 18: `PRIMARY_COLOR = "#0021A5"`
**What it does:** Creates a variable (like a labeled box) that holds a blue color code  
**Think of it like:** Mixing paint - this recipe makes a specific shade of blue  
**The color:** Dark blue (like University of Florida blue)

### Line 19: `SECONDARY_COLOR = "#FA4616"`  
**What it does:** Creates a variable that holds an orange color code  
**The color:** Bright orange (like University of Florida orange)

### Line 20: `ACCENT_COLOR = "#28A745"`
**What it does:** Creates a variable that holds a green color code  
**The color:** Green (for success messages and highlights)

### Line 21: `st.set_page_config(page_title="Enhanced Florida Drug Overdose Analytics", layout="wide")`
**What it does:** Tells Streamlit how to set up our webpage  
**What each part does:**
- **page_title:** Sets the title that appears in the browser tab
- **layout="wide":** Makes the page use the full width of the screen instead of a narrow column

---

## PART 3: LOADING THE DATA (Lines 23-40) {#part-3}

### Line 23: `@st.cache_data`
**What it does:** Puts a special sticky note on the next function  
**Think of it like:** Telling the computer "remember this result so you don't have to do it again"  
**Why it helps:** Loading data is slow, so we only want to do it once

### Line 24: `def load_data():`
**What it does:** Starts creating a function (like a recipe) called "load_data"  
**Think of it like:** Writing instructions that we can follow over and over  
**Functions are:** Sets of instructions with a name

### Line 25: `"""Load and preprocess the drug overdose data"""`
**What it does:** This is a comment that explains what this function does  
**Think of it like:** A sticky note explaining the purpose  
**Note:** Comments are for humans - the computer ignores them

### Line 26: `df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)`
**What it does:** Tells pandas to read a CSV file and put it in a variable called "df"  
**Think of it like:** Opening an Excel file and loading it into memory  
**What each part means:**
- **df:** Short for "dataframe" (like a spreadsheet)
- **pd.read_csv:** Pandas function to read CSV files
- **"mecDecedentTable (2).csv":** The name of our data file
- **low_memory=False:** Use more computer memory to read faster

### Line 27: `df.columns = df.columns.str.strip().str.lower()`
**What it does:** Cleans up the column names  
**Think of it like:** Organizing your folders by removing extra spaces and making everything lowercase  
**Example:** Changes "  AGE  " to "age"

### Line 28: `df['date'] = pd.to_datetime(df['date'], errors='coerce')`
**What it does:** Converts the date column to a proper date format  
**Think of it like:** Teaching the computer that "2022-05-15" is a date, not just text  
**errors='coerce':** If something can't be converted, make it blank instead of crashing

### Line 29: `df['year'] = df['date'].dt.year`
**What it does:** Creates a new column called 'year' by extracting just the year from dates  
**Think of it like:** Taking "2022-05-15" and just keeping "2022"  
**Why useful:** Makes it easier to filter by year

### Lines 31-35: Creating age groups
```python
df['age_group'] = pd.cut(df['age'], 
                        bins=[0, 18, 25, 35, 45, 55, 65, 100], 
                        labels=['0-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'])
```
**What it does:** Creates a new column that puts people into age groups  
**Think of it like:** Sorting people into bins based on their age  
**Example:** Someone who is 28 goes into the "25-34" group  
**Why useful:** Makes it easier to make charts by age group

### Line 37: `return df`
**What it does:** Gives back the cleaned-up data to whoever asked for it  
**Think of it like:** Handing over the finished product

### Line 39: `df = load_data()`
**What it does:** Actually runs our function and stores the result  
**Think of it like:** Following the recipe and putting the result in a container called "df"

---

---

## **PART 15: NEW DEMOGRAPHIC BREAKDOWN FUNCTION (Lines 970-1224)** {#part-15}

This is a brand new feature that creates detailed breakdowns of drug overdoses by demographics like race, sex, age group, and county.

### **Function Start (Line 970)**

**Line 970:** `def create_demographic_breakdown(dff, query):`
**What it does:** Starts a function that creates demographic analysis
**Think of it like:** A function that sorts people into groups and counts them
**Purpose:** Shows how drug overdoses affect different groups of people

### **Determining Which Demographic to Analyze (Lines 973-990)**

**Lines 973-990:** Figure out what demographic the user wants
**What it does:** Looks at the user's question to see if they want breakdown by:
- **Race** (if they say "by race" or "race")
- **Sex** (if they say "by sex", "by gender", or "sex")
- **Age Group** (if they say "by age" or "age group")
- **County** (if they say "by county" or "county")

**Think of it like:** A smart assistant figuring out how you want to group people

**Example:** If someone asks "fentanyl deaths by race", it knows to group by race

### **Checking Data Availability (Lines 992-995)**

**Lines 992-995:** Make sure the demographic data exists
**What it does:** Checks if the demographic column is actually in our dataset
**Think of it like:** Making sure you have the right ingredients before cooking
**Why important:** Prevents crashes if data is missing

### **Getting Drug Information (Lines 997-1001)**

**Lines 997-1001:** Figure out which drug we're analyzing
**What it does:** Gets the drug name from the user's query
**Default:** If no drug specified, shows "Unknown Drug"
**Think of it like:** Reading the label to know what we're counting

### **Creating the Title (Lines 1003-1008)**

**Lines 1003-1008:** Create a descriptive title for the analysis
**What it does:** Makes titles like "Fentanyl Overdoses by Race in 2022"
**Think of it like:** Writing a clear headline for a newspaper article
**Includes:** Drug name, demographic type, and year (if specified)

### **Processing the Data (Lines 1010-1020)**

**Lines 1010-1020:** Count and organize the demographic data
**What it does:**
- Groups the data by the demographic variable (race, sex, etc.)
- Counts how many cases are in each group
- Sorts from highest to lowest count
- Removes empty or missing values

**Think of it like:** Sorting people into groups and counting each group

### **Creating the Visualization Layout (Lines 1022-1025)**

**Lines 1022-1025:** Set up the page layout
**What it does:** Creates two columns - one for charts, one for tables
**Think of it like:** Dividing a page into two sections
**Layout:** Chart takes up 2/3 of the space, table takes up 1/3

### **Creating the Bar Chart (Lines 1027-1050)**

**Lines 1027-1050:** Make an interactive bar chart
**What it does:**
- Creates a colorful bar chart showing each demographic group
- Taller bars mean more deaths in that group
- Colors get darker for higher numbers
- Rotates labels if there are many groups

**Think of it like:** Making a visual graph where you can see which groups are most affected

### **Creating the Data Table (Lines 1052-1070)**

**Lines 1052-1070:** Make a detailed table
**What it does:**
- Shows exact numbers for each demographic group
- Calculates percentages for each group
- Formats everything nicely with proper column names

**Think of it like:** Making a detailed report card with exact numbers and percentages

### **Summary Statistics (Lines 1072-1085)**

**Lines 1072-1085:** Show key summary numbers
**What it does:** Creates four summary boxes showing:
- **Total Deaths:** Overall number of deaths
- **Most Affected Group:** Which demographic group has the most deaths
- **Highest Count:** The exact number for the most affected group
- **Highest Percentage:** What percent of total deaths this represents

**Think of it like:** Highlighting the most important facts at the top

### **Detailed Breakdown List (Lines 1087-1093)**

**Lines 1087-1093:** Show a numbered list of all groups
**What it does:** Lists each demographic group with exact numbers and percentages
**Think of it like:** Making a ranked list from most affected to least affected
**Format:** "1. White: 32,467 deaths (85.6%)"

### **Multi-Drug Analysis (Lines 1095-1125)**

**Lines 1095-1125:** Special analysis for multiple drugs
**What it does:** If the user asked about multiple drugs, creates a comparison chart
**Think of it like:** Comparing how different drugs affect different demographic groups
**Example:** Showing how fentanyl vs cocaine affects different races

### **Download Option (Lines 1127-1135)**

**Lines 1127-1135:** Let users download the data
**What it does:** Creates a button to download the results as a CSV file
**Think of it like:** Giving users a way to save the data for later use in Excel
**File name:** Automatically names the file based on the drug and demographic

---

## **PART 16: UPDATED MAIN QUERY HANDLER (Lines 1225-1337)** {#part-16}

The main query handler now includes the new demographic breakdown feature.

### **New Intent Handling (Lines 1283-1285)**

**Lines 1283-1285:** Handle demographic breakdown requests
```python
elif intent == "demographic_breakdown":
    return create_demographic_breakdown(dff, query)
```

**What it does:** When someone asks for a demographic breakdown, calls our new function
**Think of it like:** A traffic director sending demographic requests to the right department
**Examples of queries this handles:**
- "Fentanyl overdoses by race"
- "Cocaine deaths broken down by sex"
- "Heroin overdoses by age group"

---

## **PART 17: UPDATED MAIN EXECUTION (Lines 1339-1406)** {#part-17}

The main execution section remains the same but now handles the new demographic breakdown queries.

### **Enhanced Query Processing**

The app now recognizes and processes these new types of questions:
- **"[Drug] overdoses by race"** → Creates race breakdown
- **"[Drug] deaths broken down by sex"** → Creates sex breakdown
- **"[Drug] overdoses by age group"** → Creates age group breakdown
- **"[Drug] deaths by county"** → Creates county breakdown

### **Integration with Existing Features**

The demographic breakdown works with all existing features:
- **Year filtering:** "Fentanyl overdoses by race in 2022"
- **County filtering:** "Cocaine deaths by sex in Miami-Dade"
- **Cause vs Present:** "Deaths caused by heroin by age group"
- **Multiple drugs:** "Fentanyl and cocaine overdoses by race"

---

## **SUMMARY: COMPLETE APP FUNCTIONALITY**

### **What the Enhanced App Can Do:**

1. **Basic Queries:** Count deaths, filter by year/county/drug
2. **Visualizations:** Pie charts, bar charts, line charts, heatmaps, scatter plots, maps
3. **Statistical Analysis:** Correlation analysis between variables
4. **Data Tables:** Comprehensive drug summary tables
5. **🆕 Demographic Analysis:** Detailed breakdowns by race, sex, age, county
6. **Error Handling:** Validates queries, corrects spelling, provides helpful errors
7. **Fuzzy Matching:** Understands misspelled drug names

### **New Example Queries That Work:**

- **"Fentanyl overdoses by race"** → Bar chart + table showing racial breakdown
- **"Cocaine deaths broken down by sex in 2022"** → Sex breakdown for 2022 only
- **"Heroin overdoses by age group"** → Age group analysis with percentages
- **"Methamphetamine deaths by county"** → County-by-county breakdown
- **"Oxycodone overdoses breakdown by race"** → Detailed racial analysis

### **How Everything Works Together:**

1. **User types a demographic question** (e.g., "fentanyl by race")
2. **Validation function** checks if it makes sense
3. **Parser function** identifies it as a demographic breakdown request
4. **Filter function** narrows data to the requested drug/year/county
5. **Demographic function** creates charts, tables, and analysis
6. **Results displayed** with interactive visualizations and downloadable data

The app is now a comprehensive tool for analyzing drug overdose patterns across different demographic groups, providing both visual and statistical insights that can inform public health decisions.

---

## **🆕 NEW FEATURE SPOTLIGHT: DEMOGRAPHIC BREAKDOWN**

### **What This Feature Does:**
The demographic breakdown feature allows users to see how drug overdoses affect different groups of people. Instead of just knowing the total number of deaths, you can now see:

- **Which racial groups** are most affected by specific drugs
- **How drug deaths differ between males and females**
- **Which age groups** have the highest overdose rates
- **Which counties** have the most deaths from specific drugs

### **How to Use It:**
Simply ask questions like:
- "Fentanyl overdoses by race"
- "Cocaine deaths broken down by sex"
- "Heroin overdoses by age group"
- "Methamphetamine deaths by county"

### **What You Get:**
1. **Interactive Bar Chart** showing visual comparison between groups
2. **Detailed Data Table** with exact numbers and percentages
3. **Summary Statistics** highlighting the most affected groups
4. **Downloadable Data** for further analysis in Excel
5. **Multi-Drug Comparisons** when analyzing multiple drugs

### **Real-World Example:**
If you ask "Fentanyl overdoses by race", you might see:
- White: 32,467 deaths (85.6%)
- Black: 3,830 deaths (10.1%)
- Hispanic: 1,262 deaths (3.3%)
- Other: 190 deaths (0.5%)
- Asian: 120 deaths (0.3%)

This information helps public health officials understand which communities need the most help and resources.

### **Technical Implementation:**
The feature works by:
1. **Detecting demographic keywords** in user queries
2. **Filtering data** to the requested drug and time period
3. **Grouping records** by the demographic variable
4. **Counting and calculating percentages** for each group
5. **Creating visualizations** and tables
6. **Providing download options** for the data

This makes complex demographic analysis as simple as asking a question in plain English!
