#!/usr/bin/env python3
"""
Test correlation functionality specifically
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import parse_query, analyze_correlation, apply_filters, df
    
    print("✅ Testing correlation functionality")
    
    # Test correlation query
    query_text = "correlation between age and fentanyl"
    query = parse_query(query_text)
    
    print(f"\n📝 Query: '{query_text}'")
    print(f"   Parsed drugs: {query.get('drugs')}")
    print(f"   Intent: {query.get('intent')}")
    print(f"   Original text: {query.get('original_text')}")
    
    # Apply filters to get relevant data
    dff = apply_filters(df, query)
    print(f"   Filtered data: {len(dff):,} records")
    
    # Test the correlation analysis directly
    print(f"\n🔗 Testing correlation analysis...")
    
    # Simulate what analyze_correlation should do
    variables = []
    text_lower = query.get("original_text", "").lower()
    
    # Check for drug variables
    if query.get("drugs"):
        for drug in query["drugs"]:
            if drug in dff.columns:
                # Convert drug columns to binary (present/not present)
                drug_binary = (dff[drug].isin(query["status"])).astype(int)
                variables.append((drug, drug_binary))
                print(f"   Added drug variable: {drug} (binary)")

    # Check for demographic/numeric variables mentioned in the query
    numeric_cols = ['age', 'causesubstancecount', 'presentsubstancecount', 'totalsubstancecount']
    for col in numeric_cols:
        if col in dff.columns and col in text_lower:
            variables.append((col, dff[col]))
            print(f"   Added numeric variable: {col}")
    
    # Special handling for common correlation queries
    if "age" in text_lower and len(variables) == 1:
        # User wants to correlate something with age
        if 'age' in dff.columns:
            variables.append(('age', dff['age']))
            print(f"   Added age variable (special handling)")
    
    print(f"   Total variables found: {len(variables)}")
    
    if len(variables) >= 2:
        print("   ✅ Correlation analysis should work!")
        
        # Create correlation data
        corr_data = pd.DataFrame()
        for var_name, var_data in variables:
            corr_data[var_name] = var_data
        
        # Remove missing values
        corr_data = corr_data.dropna()
        print(f"   Data for correlation: {len(corr_data):,} records")
        
        if not corr_data.empty:
            # Calculate correlation matrix
            corr_matrix = corr_data.corr()
            print(f"   Correlation matrix shape: {corr_matrix.shape}")
            print(f"   Correlation values:")
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    var1, var2 = corr_matrix.columns[i], corr_matrix.columns[j]
                    corr_val = corr_matrix.iloc[i, j]
                    print(f"     {var1} & {var2}: {corr_val:.3f}")
    else:
        print("   ❌ Not enough variables for correlation")
    
    print("\n🎉 Correlation test completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
