#!/usr/bin/env python3
"""
Verify that font sizes have actually increased in the Site Information tab
"""

import re
import subprocess
import time
import os

def check_css_rules():
    """Check the CSS rules in the code"""
    try:
        with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("🔍 VERIFYING FONT SIZE INCREASES IN CODE")
        print("=" * 70)
        
        # Check for aggressive CSS targeting
        aggressive_checks = [
            ('font-size: 24px !important', 'Base font size set to 24px'),
            ('font-size: 26px !important', 'Table headers set to 26px'),
            ('font-size: 32px !important', 'Metric values set to 32px'),
            ('font-size: 48px !important', 'Main title set to 48px'),
            ('.stTabs > div > div:nth-child(2)', 'Second tab specifically targeted'),
            ('.about-section, .about-section *', 'Universal selector applied'),
            ('HTML table with large fonts', 'Custom HTML tables implemented')
        ]
        
        print("\n📝 CSS Rule Verification:")
        print("-" * 50)
        
        for check, description in aggressive_checks:
            if check in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - NOT FOUND")
        
        # Count font-size declarations
        font_size_count = len(re.findall(r'font-size:\s*\d+px', content))
        important_count = len(re.findall(r'!important', content))
        
        print(f"\n📊 CSS Statistics:")
        print(f"   Font-size declarations: {font_size_count}")
        print(f"   !important overrides: {important_count}")
        
        # Check for HTML table implementation
        html_table_count = content.count("html_table")
        print(f"   Custom HTML tables: {html_table_count}")
        
        return font_size_count >= 20 and important_count >= 30
        
    except Exception as e:
        print(f"❌ Error checking CSS rules: {e}")
        return False

def run_streamlit_test():
    """Run Streamlit app and check if it starts successfully"""
    try:
        print("\n🚀 TESTING STREAMLIT APP STARTUP")
        print("-" * 50)
        
        # Start Streamlit in background
        process = subprocess.Popen(
            ['streamlit', 'run', 'drug_query_chatbot_app.py', '--server.port', '8510'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a few seconds for startup
        time.sleep(8)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Streamlit app started successfully")
            print("✅ App is running on http://localhost:8510")
            
            # Terminate the process
            process.terminate()
            process.wait()
            
            return True
        else:
            stdout, stderr = process.communicate()
            print("❌ Streamlit app failed to start")
            print(f"Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Streamlit: {e}")
        return False

def main():
    print("🧪 COMPREHENSIVE FONT SIZE VERIFICATION")
    print("=" * 70)
    
    # Test 1: Check CSS rules
    css_ok = check_css_rules()
    
    # Test 2: Test Streamlit startup
    app_ok = run_streamlit_test()
    
    # Summary
    print(f"\n📋 VERIFICATION SUMMARY")
    print("=" * 70)
    
    if css_ok:
        print("✅ CSS Rules: Comprehensive font size overrides implemented")
    else:
        print("❌ CSS Rules: Issues found with font size implementation")
    
    if app_ok:
        print("✅ App Startup: Streamlit app runs without errors")
    else:
        print("❌ App Startup: Streamlit app has startup issues")
    
    print(f"\n🎯 FONT SIZE IMPLEMENTATION STATUS:")
    
    if css_ok and app_ok:
        print("🎉 SUCCESS: Font size increases should be visible!")
        print("\n📏 Expected font sizes in Site Information tab:")
        print("   - Main title: 48px")
        print("   - Section headers: 36px")
        print("   - Subsection headers: 32px")
        print("   - Body text: 24px")
        print("   - Table headers: 26px")
        print("   - Table data: 24px")
        print("   - Metric values: 32px")
        print("   - All other text: 24px")
        
        print(f"\n🚀 TO VERIFY VISUALLY:")
        print("   1. Run: streamlit run drug_query_chatbot_app.py")
        print("   2. Click on 'Site Information and Query Suggestions' tab")
        print("   3. Check that ALL text is noticeably larger")
        print("   4. Verify tables have large, readable fonts")
        print("   5. Confirm uniform font sizes throughout")
        
    else:
        print("⚠️  ISSUES DETECTED: Font size increases may not be working")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Check CSS implementation")
        print("   2. Verify Streamlit app starts correctly")
        print("   3. Clear browser cache")
        print("   4. Try hard refresh (Ctrl+F5)")
    
    print(f"\n💡 If fonts still appear small:")
    print("   - The CSS might be overridden by Streamlit defaults")
    print("   - Browser caching might be preventing updates")
    print("   - Try opening in incognito/private browsing mode")
    print("   - Check browser developer tools for CSS conflicts")

if __name__ == "__main__":
    main()
