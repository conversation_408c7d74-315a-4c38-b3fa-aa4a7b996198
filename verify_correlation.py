#!/usr/bin/env python3
"""
Verify the correlation functionality works as expected
"""

import pandas as pd
import numpy as np
from scipy.stats import pearsonr
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import parse_query, apply_filters, df, drug_columns
    
    print("🔗 CORRELATION VERIFICATION")
    print("=" * 50)
    
    # Test the exact query you mentioned
    query_text = "correlation between deaths caused by fentanyl and deaths caused by cocaine"
    print(f"Query: '{query_text}'")
    
    # Parse and filter
    query = parse_query(query_text)
    dff = apply_filters(df, query)
    
    print(f"\nParsed results:")
    print(f"  - Intent: {query.get('intent')}")
    print(f"  - Drugs found: {query.get('drugs')}")
    print(f"  - Status: {query.get('status')}")
    print(f"  - Filtered records: {len(dff):,}")
    
    # Manual correlation calculation to show what the app will display
    print(f"\n📊 CORRELATION ANALYSIS RESULTS:")
    print("-" * 40)
    
    # Get the drug data
    fentanyl_binary = (dff['fentanyl'].isin(query["status"])).astype(int)
    cocaine_binary = (dff['cocaine'].isin(query["status"])).astype(int)
    
    # Create correlation data
    corr_data = pd.DataFrame({
        'fentanyl': fentanyl_binary,
        'cocaine': cocaine_binary
    }).dropna()
    
    print(f"Sample size: {len(corr_data):,} records")
    print(f"Fentanyl positive cases: {fentanyl_binary.sum():,}")
    print(f"Cocaine positive cases: {cocaine_binary.sum():,}")
    
    # Calculate correlation
    correlation, p_value = pearsonr(corr_data['fentanyl'], corr_data['cocaine'])
    
    print(f"\n🔗 CORRELATION RESULTS:")
    print(f"  - Pearson correlation: {correlation:.4f}")
    print(f"  - P-value: {p_value:.6f}")
    print(f"  - Significance: {'Significant' if p_value < 0.05 else 'Not significant'} (α = 0.05)")
    
    # Interpretation
    if abs(correlation) < 0.1:
        strength = "very weak"
    elif abs(correlation) < 0.3:
        strength = "weak"
    elif abs(correlation) < 0.5:
        strength = "moderate"
    elif abs(correlation) < 0.7:
        strength = "strong"
    else:
        strength = "very strong"
    
    direction = "positive" if correlation > 0 else "negative"
    
    print(f"\n📈 INTERPRETATION:")
    print(f"  - Correlation strength: {strength}")
    print(f"  - Direction: {direction}")
    print(f"  - Meaning: There is a {strength} {direction} correlation between")
    print(f"    fentanyl and cocaine deaths.")
    
    if correlation > 0:
        print(f"  - This suggests that cases with fentanyl are somewhat more")
        print(f"    likely to also have cocaine present.")
    
    # Test a few more correlation queries
    print(f"\n🧪 TESTING OTHER CORRELATION QUERIES:")
    print("-" * 45)
    
    other_queries = [
        "correlation between fentanyl and cocaine",
        "correlation between age and fentanyl",
        "correlation between heroin and methamphetamine"
    ]
    
    for test_query in other_queries:
        parsed = parse_query(test_query)
        filtered = apply_filters(df, parsed)
        print(f"✅ '{test_query}'")
        print(f"   Drugs: {parsed.get('drugs')}")
        print(f"   Records: {len(filtered):,}")
    
    print(f"\n🎉 CORRELATION FUNCTIONALITY IS WORKING!")
    print("=" * 50)
    print("You can now use queries like:")
    print("- 'correlation between deaths caused by fentanyl and deaths caused by cocaine'")
    print("- 'correlation between fentanyl and cocaine'")
    print("- 'correlation between age and fentanyl'")
    print("- 'correlation between heroin and methamphetamine'")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
