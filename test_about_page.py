#!/usr/bin/env python3
"""
Test the new About page functionality
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import df, drug_columns
    
    print("🧪 TESTING ABOUT PAGE DATA")
    print("=" * 60)
    
    # Test dataset overview metrics
    print(f"📊 Dataset Overview:")
    print(f"   Total Records: {len(df):,}")
    print(f"   Total Drugs: {len(drug_columns)}")
    
    if 'year' in df.columns:
        available_years = sorted(df['year'].dropna().unique())
        print(f"   Year Range: {min(available_years)}-{max(available_years)}")
        print(f"   Available Years: {[int(year) for year in available_years]}")
    
    if 'county name' in df.columns:
        available_counties = df['county name'].dropna().nunique()
        print(f"   Counties: {available_counties}")
    
    # Test drug statistics
    print(f"\n💊 Drug Statistics:")
    print("-" * 40)
    
    drug_stats = []
    for drug in sorted(drug_columns)[:10]:  # Test first 10 drugs
        if drug in df.columns:
            caused_count = (df[drug] == 'C').sum()
            present_count = (df[drug] == 'P').sum()
            total_count = caused_count + present_count
            if total_count > 0:
                drug_stats.append({
                    'Drug': drug,
                    'Caused': caused_count,
                    'Present': present_count,
                    'Total': total_count
                })
    
    print(f"   Top 10 drugs by total cases:")
    drug_stats_sorted = sorted(drug_stats, key=lambda x: x['Total'], reverse=True)
    for i, drug_stat in enumerate(drug_stats_sorted[:10], 1):
        print(f"   {i:2d}. {drug_stat['Drug']:<20}: {drug_stat['Total']:>6,} total ({drug_stat['Caused']:>5,} caused, {drug_stat['Present']:>5,} present)")
    
    # Test county statistics
    print(f"\n🏛️ County Statistics:")
    print("-" * 40)
    
    if 'county name' in df.columns:
        counties = sorted(df['county name'].dropna().unique())
        print(f"   Total counties: {len(counties)}")
        print(f"   Sample counties: {counties[:10]}")
        
        county_counts = df['county name'].value_counts().head(10)
        print(f"   Top 10 counties by cases:")
        for i, (county, count) in enumerate(county_counts.items(), 1):
            print(f"   {i:2d}. {county:<25}: {count:>6,} cases")
    
    # Test year statistics
    print(f"\n📅 Year Statistics:")
    print("-" * 40)
    
    if 'year' in df.columns:
        year_counts = df['year'].value_counts().sort_index()
        print(f"   Cases by year:")
        for year, count in year_counts.items():
            print(f"   {int(year)}: {count:>6,} cases")
    
    # Test data completeness
    print(f"\n🔍 Data Completeness Check:")
    print("-" * 40)
    
    required_columns = ['year', 'county name', 'age', 'sex', 'race']
    for col in required_columns:
        if col in df.columns:
            non_null_count = df[col].notna().sum()
            null_count = df[col].isna().sum()
            completeness = (non_null_count / len(df)) * 100
            print(f"   {col:<15}: {completeness:5.1f}% complete ({non_null_count:,} non-null, {null_count:,} null)")
        else:
            print(f"   {col:<15}: ❌ Column not found")
    
    # Test demographic data
    print(f"\n👥 Demographic Data:")
    print("-" * 40)
    
    demographics = {
        'sex': df.get('sex', pd.Series()).value_counts().head(5),
        'race': df.get('race', pd.Series()).value_counts().head(5),
        'age_group': df.get('age_group', pd.Series()).value_counts().head(5)
    }
    
    for demo_name, demo_data in demographics.items():
        if not demo_data.empty:
            print(f"   {demo_name.title()} breakdown:")
            for value, count in demo_data.items():
                if pd.notna(value) and value != '':
                    percentage = (count / len(df)) * 100
                    print(f"     {value:<20}: {count:>6,} ({percentage:4.1f}%)")
        else:
            print(f"   {demo_name.title()}: No data available")
    
    print(f"\n🎉 ABOUT PAGE DATA TEST COMPLETED!")
    print("=" * 60)
    
    print(f"✅ Dataset overview metrics work")
    print(f"✅ Drug statistics calculated successfully")
    print(f"✅ County data available and processed")
    print(f"✅ Year data shows trends over time")
    print(f"✅ Demographic breakdowns functional")
    
    print(f"\n💡 The About page will show:")
    print(f"   - Complete list of all {len(drug_columns)} drugs")
    print(f"   - All {len(counties) if 'counties' in locals() else 'available'} counties")
    print(f"   - Years {min(available_years) if 'available_years' in locals() else 'N/A'} to {max(available_years) if 'available_years' in locals() else 'N/A'}")
    print(f"   - Comprehensive query limitations and tips")
    print(f"   - Data source and methodology information")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
