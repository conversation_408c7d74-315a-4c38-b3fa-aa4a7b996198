#!/usr/bin/env python3
"""
Test that the specific sections now have larger fonts:
- Query Limitations & Inaccurate Results
- Data Source & Methodology  
- Need Help
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING SPECIFIC SECTIONS FONT INCREASES")
    print("=" * 70)
    
    # Test 1: Query Limitations & Inaccurate Results (Expandable sections)
    print("\n📝 Test 1: Query Limitations & Inaccurate Results")
    print("-" * 60)
    
    expandable_checks = [
        ('.stExpander,', 'Expandable sections targeted'),
        ('.stExpander *,', 'All expandable content targeted'),
        ('font-size: 24px !important', 'Base expandable font set to 24px'),
        ('font-size: 28px !important', 'Expandable titles set to 28px'),
        ('.stExpander .stMarkdown *', 'All markdown in expandables targeted'),
        ('.stExpander .stMarkdown li', 'List items in expandables targeted'),
        ('.stExpander .stMarkdown h1', 'H1 headers in expandables targeted'),
        ('.stExpander .stMarkdown h2', 'H2 headers in expandables targeted'),
        ('.stExpander .stMarkdown h3', 'H3 headers in expandables targeted'),
        ('.stExpander .stMarkdown strong', 'Bold text in expandables targeted')
    ]
    
    for check, description in expandable_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 2: Data Source & Methodology
    print("\n📝 Test 2: Data Source & Methodology")
    print("-" * 60)
    
    methodology_checks = [
        ('font-size: 24px; line-height: 1.6', 'Base methodology text set to 24px'),
        ('font-size: 28px; font-weight: bold', 'Methodology headers set to 28px'),
        ('<h3 style="font-size: 28px', 'HTML headers with large fonts'),
        ('<ul style="font-size: 24px', 'HTML lists with large fonts'),
        ('<li><strong>Source:</strong>', 'Dataset information with large fonts'),
        ('<li><strong>Each record:</strong>', 'Data structure with large fonts'),
        ('unsafe_allow_html=True', 'HTML rendering enabled for large fonts')
    ]
    
    for check, description in methodology_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 3: Need Help section
    print("\n📝 Test 3: Need Help Section")
    print("-" * 60)
    
    help_checks = [
        ('<h3 style="font-size: 28px; font-weight: bold;">Getting Started</h3>', 'Getting Started header large'),
        ('<h3 style="font-size: 28px; font-weight: bold;">Common Issues</h3>', 'Common Issues header large'),
        ('<ul style="font-size: 24px; line-height: 1.6;">', 'Help lists with large fonts'),
        ('background-color: #e1f5fe', 'Custom styled info box'),
        ('<p style="font-size: 24px', 'Pro tip with large font'),
        ('💡 <strong>Pro Tip:</strong>', 'Pro tip content with large styling')
    ]
    
    for check, description in help_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 4: Count specific font size implementations
    print("\n📝 Test 4: Font Size Implementation Count")
    print("-" * 60)
    
    # Count different font sizes used
    font_24px = len(re.findall(r'font-size:\s*24px', content))
    font_28px = len(re.findall(r'font-size:\s*28px', content))
    font_32px = len(re.findall(r'font-size:\s*32px', content))
    font_36px = len(re.findall(r'font-size:\s*36px', content))
    
    print(f"✅ 24px font declarations: {font_24px}")
    print(f"✅ 28px font declarations: {font_28px}")
    print(f"✅ 32px font declarations: {font_32px}")
    print(f"✅ 36px font declarations: {font_36px}")
    
    total_large_fonts = font_24px + font_28px + font_32px + font_36px
    print(f"✅ Total large font declarations: {total_large_fonts}")
    
    # Test 5: Check for HTML styling implementation
    print("\n📝 Test 5: HTML Styling Implementation")
    print("-" * 60)
    
    html_checks = [
        ('unsafe_allow_html=True', 'HTML rendering enabled'),
        ('<div style="font-size: 24px', 'HTML divs with large fonts'),
        ('<h3 style="font-size: 28px', 'HTML headers with large fonts'),
        ('<ul style="font-size: 24px', 'HTML lists with large fonts'),
        ('<p style="font-size: 24px', 'HTML paragraphs with large fonts'),
        ('line-height: 1.6', 'Improved line spacing')
    ]
    
    for check, description in html_checks:
        count = content.count(check)
        if count > 0:
            print(f"✅ {description} (found {count} times)")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    print(f"\n🎉 SPECIFIC SECTIONS FONT TEST COMPLETED!")
    print("=" * 70)
    
    # Summary of implementations
    implementations = []
    if '.stExpander *,' in content:
        implementations.append("✅ Query Limitations expandables: 24px+ fonts")
    if 'font-size: 28px; font-weight: bold' in content:
        implementations.append("✅ Data Source & Methodology: 24-28px fonts")
    if '<h3 style="font-size: 28px; font-weight: bold;">Getting Started</h3>' in content:
        implementations.append("✅ Need Help section: 24-28px fonts")
    if total_large_fonts >= 50:
        implementations.append("✅ Comprehensive font coverage: 50+ declarations")
    
    print(f"\n📋 Implementation Summary:")
    for impl in implementations:
        print(f"   {impl}")
    
    if len(implementations) >= 3:
        print(f"\n🎉 ALL SPECIFIC SECTIONS SUCCESSFULLY UPDATED!")
        print(f"   📏 Query Limitations: 24px content, 28px titles")
        print(f"   📏 Data Source & Methodology: 24px text, 28px headers")
        print(f"   📏 Need Help: 24px text, 28px headers")
        print(f"   📏 All sections: Consistent large fonts with HTML styling")
        print(f"   📏 Total font declarations: {total_large_fonts}")
    else:
        print(f"\n⚠️  Some sections may need additional updates")
    
    print(f"\n🚀 Ready to test the updated sections!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Check: Site Information tab")
    print(f"   Verify: These sections now have large fonts:")
    print(f"   - Query Limitations & Inaccurate Results (expandable content)")
    print(f"   - Data Source & Methodology (all text)")
    print(f"   - Need Help (Getting Started & Common Issues)")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
