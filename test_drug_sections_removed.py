#!/usr/bin/env python3
"""
Test that the A-H, I-P, Q-Z section headers have been removed from Available Drugs
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING DRUG SECTION HEADERS REMOVAL")
    print("=" * 60)
    
    # Test 1: Check that section headers are removed
    print("\n📝 Test 1: Section Headers Removal")
    print("-" * 40)
    
    removed_headers = [
        ('st.markdown("**A-H:**")', 'A-H header removed'),
        ('st.markdown("**I-P:**")', 'I-P header removed'),
        ('st.markdown("**Q-Z:**")', 'Q-Z header removed'),
        ('"**A-H:**"', 'A-H text removed'),
        ('"**I-P:**"', 'I-P text removed'),
        ('"**Q-Z:**"', 'Q-Z text removed')
    ]
    
    for check, description in removed_headers:
        if check not in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - STILL PRESENT")
    
    # Test 2: Check that drug names are still present
    print("\n📝 Test 2: Drug Names Still Present")
    print("-" * 40)
    
    drug_structure_checks = [
        ('for drug in drugs_sorted[:drugs_per_col]:', 'First column drug loop present'),
        ('for drug in drugs_sorted[drugs_per_col:drugs_per_col*2]:', 'Second column drug loop present'),
        ('for drug in drugs_sorted[drugs_per_col*2:]:', 'Third column drug loop present'),
        ("st.markdown(f\"<p style='font-size: 22px; margin: 8px 0;'>• {drug}</p>\"", 'Drug name styling present'),
        ('col1, col2, col3 = st.columns(3)', 'Three column layout present'),
        ('drugs_per_col = len(drugs_sorted) // 3 + 1', 'Drug distribution calculation present')
    ]
    
    for check, description in drug_structure_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 3: Check that the structure is clean
    print("\n📝 Test 3: Clean Structure Verification")
    print("-" * 40)
    
    # Count the occurrences of drug display code
    drug_display_count = content.count("st.markdown(f\"<p style='font-size: 22px; margin: 8px 0;'>• {drug}</p>\"")
    column_count = content.count("with col")
    
    print(f"✅ Drug display statements: {drug_display_count}")
    print(f"✅ Column statements: {column_count}")
    
    if drug_display_count == 3:
        print("✅ Correct number of drug display loops (3)")
    else:
        print(f"❌ Expected 3 drug display loops, found {drug_display_count}")
    
    # Test 4: Check that other sections are unaffected
    print("\n📝 Test 4: Other Sections Unaffected")
    print("-" * 40)
    
    other_sections = [
        ('Drug Usage Statistics', 'Drug statistics section present'),
        ('Available Data for Queries', 'Available data section present'),
        ('Query Limitations', 'Query limitations section present'),
        ('Data Source & Methodology', 'Data source section present'),
        ('Need Help', 'Help section present')
    ]
    
    for check, description in other_sections:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    print(f"\n🎉 DRUG SECTION HEADERS REMOVAL TEST COMPLETED!")
    print("=" * 60)
    
    # Summary
    headers_removed = 0
    if 'st.markdown("**A-H:**")' not in content:
        headers_removed += 1
    if 'st.markdown("**I-P:**")' not in content:
        headers_removed += 1
    if 'st.markdown("**Q-Z:**")' not in content:
        headers_removed += 1
    
    structure_intact = (
        'for drug in drugs_sorted[:drugs_per_col]:' in content and
        'for drug in drugs_sorted[drugs_per_col:drugs_per_col*2]:' in content and
        'for drug in drugs_sorted[drugs_per_col*2:]:' in content and
        drug_display_count == 3
    )
    
    print(f"\n📋 Summary:")
    print(f"   Headers removed: {headers_removed}/3")
    print(f"   Drug structure intact: {'✅ Yes' if structure_intact else '❌ No'}")
    print(f"   Drug display loops: {drug_display_count}/3")
    
    if headers_removed == 3 and structure_intact:
        print(f"\n🎉 SUCCESS: Section headers removed, drug names preserved!")
        print(f"   ✅ A-H, I-P, Q-Z headers completely removed")
        print(f"   ✅ All drug names still displayed in 3 columns")
        print(f"   ✅ Large font styling preserved (22px)")
        print(f"   ✅ Column layout maintained")
        print(f"   ✅ Other sections unaffected")
    else:
        print(f"\n⚠️  Issues detected:")
        if headers_removed < 3:
            print(f"   - Some headers may still be present")
        if not structure_intact:
            print(f"   - Drug display structure may be damaged")
    
    print(f"\n🚀 Ready to test the updated drug list!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Check: Site Information tab > Available Data > 💊 Drugs")
    print(f"   Verify: No A-H, I-P, Q-Z headers, but all drug names visible")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
