#!/usr/bin/env python3
"""
Test that the main tab fonts are now much larger
"""

import re

try:
    # Read the updated app file
    with open('drug_query_chatbot_app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🧪 TESTING MUCH LARGER TAB FONTS")
    print("=" * 50)
    
    # Test 1: Check dramatically increased font sizes
    print("\n📝 Test 1: Dramatically Increased Font Sizes")
    print("-" * 45)
    
    font_size_checks = [
        ('font-size: 32px !important', 'Tab font size increased to 32px'),
        ('font-weight: 800 !important', 'Tab font weight increased to 800 (extra bold)'),
        ('letter-spacing: 1px !important', 'Letter spacing increased to 1px'),
        ('text-shadow: 0 2px 4px', 'Enhanced text shadow for better visibility'),
        ('text-shadow: 0 2px 5px', 'Extra enhanced text shadow for active tab')
    ]
    
    for check, description in font_size_checks:
        count = content.count(check)
        if count > 0:
            print(f"✅ {description} (found {count} times)")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 2: Check increased dimensions
    print("\n📝 Test 2: Increased Tab Dimensions")
    print("-" * 45)
    
    dimension_checks = [
        ('height: 100px !important', 'Tab height increased to 100px'),
        ('padding: 25px 50px !important', 'Tab padding increased to 25px 50px'),
        ('min-width: 350px !important', 'Minimum width increased to 350px'),
        ('margin: 10px 5px !important', 'Tab margins maintained')
    ]
    
    for check, description in dimension_checks:
        if check in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 3: Check font progression
    print("\n📝 Test 3: Font Size Progression Analysis")
    print("-" * 45)
    
    # Extract all font sizes mentioned
    font_sizes = re.findall(r'font-size:\s*(\d+)px', content)
    font_sizes = [int(size) for size in font_sizes]
    
    # Check for 32px specifically
    font_32px_count = content.count('font-size: 32px')
    font_24px_count = content.count('font-size: 24px')
    font_20px_count = content.count('font-size: 20px')
    
    print(f"✅ 32px font declarations: {font_32px_count}")
    print(f"✅ 24px font declarations: {font_24px_count}")
    print(f"✅ 20px font declarations: {font_20px_count}")
    
    if font_32px_count >= 2:
        print("✅ Main tabs now use 32px fonts")
    else:
        print("❌ Main tabs may not be using 32px fonts")
    
    # Test 4: Check weight and styling enhancements
    print("\n📝 Test 4: Weight and Styling Enhancements")
    print("-" * 45)
    
    weight_checks = [
        ('font-weight: 800', 'Extra bold font weight (800)'),
        ('font-weight: 700', 'Bold font weight (700)'),
        ('text-shadow:', 'Text shadows for depth'),
        ('letter-spacing:', 'Letter spacing for readability')
    ]
    
    for check, description in weight_checks:
        count = content.count(check)
        if count > 0:
            print(f"✅ {description} (found {count} times)")
        else:
            print(f"❌ {description} - NOT FOUND")
    
    # Test 5: Compare before and after
    print("\n📝 Test 5: Before vs After Comparison")
    print("-" * 45)
    
    improvements = []
    
    # Font size progression
    if font_32px_count >= 2:
        improvements.append("Font size: 20px → 24px → 32px (+60% from original)")
    
    # Height progression  
    if 'height: 100px' in content:
        improvements.append("Tab height: 60px → 80px → 100px (+67% from original)")
    
    # Weight progression
    if 'font-weight: 800' in content:
        improvements.append("Font weight: 600 → 700 → 800 (extra bold)")
    
    # Padding progression
    if 'padding: 25px 50px' in content:
        improvements.append("Padding: 12px 24px → 20px 40px → 25px 50px (+108% increase)")
    
    # Width progression
    if 'min-width: 350px' in content:
        improvements.append("Min width: 280px → 350px (+25% increase)")
    
    print("📊 Progressive Improvements:")
    for improvement in improvements:
        print(f"   ✅ {improvement}")
    
    print(f"\n🎉 MUCH LARGER TAB FONTS TEST COMPLETED!")
    print("=" * 50)
    
    # Final assessment
    major_increases = 0
    if font_32px_count >= 2:
        major_increases += 1
    if 'height: 100px' in content:
        major_increases += 1
    if 'font-weight: 800' in content:
        major_increases += 1
    if 'padding: 25px 50px' in content:
        major_increases += 1
    
    print(f"\n📋 Final Assessment:")
    print(f"   Major size increases implemented: {major_increases}/4")
    
    if major_increases >= 3:
        print(f"\n🎉 SUCCESS: TABS ARE NOW MUCH LARGER!")
        print(f"   📏 Font size: 32px (60% larger than original 20px)")
        print(f"   📏 Tab height: 100px (67% larger than original 60px)")
        print(f"   📏 Font weight: 800 (extra bold for maximum impact)")
        print(f"   📏 Padding: 25px 50px (much more spacious)")
        print(f"   📏 Min width: 350px (wider for better appearance)")
        print(f"   📏 Letter spacing: 1px (improved readability)")
        print(f"   📏 Text shadows: Enhanced for better visibility")
    else:
        print(f"\n⚠️  Some size increases may be incomplete")
    
    print(f"\n🚀 Ready to see the MUCH LARGER tabs!")
    print(f"   Run: streamlit run drug_query_chatbot_app.py")
    print(f"   Notice: Dramatically larger tab fonts (32px)")
    print(f"   See: Much taller tabs (100px height)")
    print(f"   Feel: More spacious and prominent appearance")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
