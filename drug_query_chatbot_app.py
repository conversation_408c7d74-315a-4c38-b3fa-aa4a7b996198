import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import re
import plotly.express as px
import plotly.graph_objects as go
import plotly.figure_factory as ff
import os
import requests
import json
import numpy as np
from scipy.stats import pearsonr, spearmanr, chi2_contingency
from difflib import get_close_matches
import warnings 
warnings.filterwarnings('ignore')

# Custom color palette
PRIMARY_COLOR = "#0021A5"
SECONDARY_COLOR = "#FA4616"
ACCENT_COLOR = "#28A745"

st.set_page_config(page_title="Enhanced Florida Drug Overdose Analytics", layout="wide")

@st.cache_data
def load_data():
    """Load and preprocess the drug overdose data"""
    df = pd.read_csv("mecDecedentTable (2).csv", low_memory=False)
    df.columns = df.columns.str.strip().str.lower()
    df['date'] = pd.to_datetime(df['date'], errors='coerce')
    df['year'] = df['date'].dt.year

    # Create age groups for demographic analysis
    df['age_group'] = pd.cut(df['age'],
                            bins=[0, 18, 25, 35, 45, 55, 65, 100],
                            labels=['<18', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'],
                            include_lowest=True)

    return df  

df = load_data()

@st.cache_data
def load_geojson():
    """Load Florida counties GeoJSON for mapping"""
    filename = "geojson-fl-counties-fips.json"
    if not os.path.exists(filename):
        url = "https://raw.githubusercontent.com/plotly/datasets/master/geojson-counties-fips.json"
        response = requests.get(url)
        if response.status_code == 200:
            with open(filename, "w") as f:
                f.write(response.text)
        else:
            st.error("Failed to download county GeoJSON file.")
            return None
    with open(filename) as f:
        geojson = json.load(f)
        # Filter to Florida only (STATEFP == '12')
        florida_features = [feat for feat in geojson["features"] if feat["properties"]["STATE"] == "12"]
        return {"type": "FeatureCollection", "features": florida_features}

counties_geojson = load_geojson()

# Column classification for robust analysis
non_drug_columns = ['unnamed: 0', 'id', 'decedentkey', 'age', 'sex', 'race', 'county', 'district',
                    'manner of death', 'cause of death', 'analytes', 'illicitfentanyl',
                    'specify other opioids', 'specify other amphetamines', 'specify other inhalants',
                    'specify other benzo', 'date', 'uf case number', 'causesubstancecount',
                    'presentsubstancecount', 'totalsubstancecount', 'poly', 'county number',
                    'county name', 'benzodiazepines group', 'amphetamines group', 'opioids group',
                    'ethanol group', 'hallucinogenics group', 'inhalants group', 'year', 'age_group']

# Drug columns are those with C/P values indicating Cause/Present
drug_columns = [col for col in df.columns if col not in non_drug_columns]

# Demographic columns for analysis
demographic_columns = ['age', 'age_group', 'sex', 'race', 'county name', 'manner of death']

# Temporal columns
temporal_columns = ['date', 'year']

# Aggregate/summary columns
aggregate_columns = ['causesubstancecount', 'presentsubstancecount', 'totalsubstancecount', 'poly']

# Group columns (drug categories)
group_columns = ['benzodiazepines group', 'amphetamines group', 'opioids group',
                'ethanol group', 'hallucinogenics group', 'inhalants group']

# Sidebar help and information
with st.sidebar:
    st.markdown(f"<h2 style='color:{PRIMARY_COLOR}'>🔎 Enhanced Query Help</h2>", unsafe_allow_html=True)

    st.markdown("### 📊 **Visualization Types**")
    st.markdown("""
    - **Pie chart**: "pie chart of fentanyl deaths by sex"
    - **Bar chart**: "bar chart of cocaine deaths by county"
    - **Line chart**: "line chart of heroin deaths over time"
    - **Heatmap**: "heatmap of drug co-occurrence"
    - **Map**: "map of fentanyl deaths in 2023"
    - **Scatter plot**: "scatter plot of age vs substance count"
    """)

    st.markdown("### 🎯 **Cause vs Present**")
    st.markdown("""
    - **Cause**: Deaths CAUSED by the drug (use "cause", "c", "C")
    - **Present**: Deaths with drug PRESENT (use "present", "p", "P")
    - **Both**: Default if not specified
    """)

    st.markdown("### 👥 **Demographics**")
    st.markdown("""
    - Age groups: teens, young adults, middle-aged, elderly
    - Sex: male, female
    - Race: white, black, hispanic, etc.
    - Counties: Miami-Dade, Broward, etc.
    """)

    st.markdown("### 🔗 **Correlations**")
    st.markdown("""
    - "correlation between fentanyl and cocaine"
    - "correlation between age and poly drug use"
    """)

    st.markdown("### 📋 **Drug Summary Tables**")
    st.markdown("""
    - "Generate a table of drug overdoses, caused and present"
    - "Generate a table of drug overdoses for 2022"
    - "Generate a table of drug overdoses for 2023 in Miami-Dade"
    - "Drug table for all years in Broward"
    """)

    st.markdown("### 👥 **Demographic Breakdowns**")
    st.markdown("""
    - "Fentanyl overdoses by race"
    - "Cocaine deaths broken down by sex"
    - "Heroin overdoses by age group"
    - "Methamphetamine deaths by county"
    - "Oxycodone overdoses breakdown by race"
    """)

    st.markdown("### 📈 **Example Queries**")
    st.markdown("""
    - "Pie chart of fentanyl cause deaths by age group in 2022"
    - "Bar chart comparing cocaine present vs cause deaths by county"
    - "Heatmap of opioid co-occurrence patterns"
    - "Correlation between methamphetamine and age"
    - "Scatter plot of age vs total substance count for males"
    - "Generate a table of drug overdoses, caused and present, for 2022"
    - "Fentanyl overdoses broken down by race"
    - "Cocaine deaths by sex in 2023"
    """)

st.markdown(
    f"<h1 style='text-align:center; color:{PRIMARY_COLOR}'>💊 Enhanced Florida Drug Overdose Analytics</h1>",
    unsafe_allow_html=True,
)

st.markdown(
    f"<p style='text-align:center; color:{SECONDARY_COLOR}; font-size:18px;'>Advanced querying with demographics, correlations, and multiple visualization types</p>",
    unsafe_allow_html=True,
)

user_input = st.text_input("Ask a question about overdose data:", "",
                          help="Try queries like 'pie chart of fentanyl deaths by sex' or 'correlation between age and cocaine use'")

def fuzzy_match_drug(input_drug, drug_list, threshold=0.6):
    """Find the best matching drug name using fuzzy matching"""
    # First try exact match
    if input_drug.lower() in [drug.lower() for drug in drug_list]:
        return next(drug for drug in drug_list if drug.lower() == input_drug.lower())

    # Try fuzzy matching
    matches = get_close_matches(input_drug.lower(), [drug.lower() for drug in drug_list],
                               n=1, cutoff=threshold)
    if matches:
        # Return the original case version
        return next(drug for drug in drug_list if drug.lower() == matches[0])

    return None

def validate_query(text):
    """Validate if the query makes sense and contains meaningful content"""
    text_lower = text.lower().strip() # Remove leading/trailing spaces

    # Check for empty or very short queries
    if len(text_lower) < 2:
        return False, "❌ Query too short. Please ask a question about drug overdose data."

    # Check for random strings (no vowels, too many consonants in a row, etc.)
    if re.match(r'^[^aeiou\s]{5,}$', text_lower):
        return False, "❌ Invalid query. Please ask a question about drug overdose data."

    # Check for common error words
    error_words = ['error', 'asdf', 'qwerty', 'random', 'gibberish']  #seems odd, why is this here?
    if any(word in text_lower for word in error_words):
        return False, "❌ Please ask a meaningful question about drug overdose data."

    # Check if query contains at least one meaningful keyword
    meaningful_keywords = [
        # Visualization types
        'chart', 'plot', 'graph', 'map', 'heatmap', 'scatter', 'pie', 'bar', 'line',
        # Analysis types
        'correlation', 'table', 'deaths', 'overdose', 'drug', 'substance',
        # Demographics
        'age', 'sex', 'race', 'county', 'male', 'female', 'year', 'month',
        # Drug-related terms
        'cause', 'caused', 'present', 'fentanyl', 'cocaine', 'heroin', 'meth',
        'opioid', 'amphetamine', 'alcohol', 'benzodiazepine',
        # Query terms
        'how many', 'show', 'display', 'generate', 'most', 'least', 'compare'
    ]

    # Also check for drug names (including fuzzy matches)
    for drug in drug_columns[:20]:  # Check first 20 common drugs #AI
        meaningful_keywords.append(drug.lower())

    has_meaningful_content = any(keyword in text_lower for keyword in meaningful_keywords)

    if not has_meaningful_content:
        return False, ("❌ I don't understand your query. Please ask about drug overdose data using terms like:\n"
                      "- Drug names (fentanyl, cocaine, heroin, etc.)\n"
                      "- Visualizations (chart, plot, map, table)\n"
                      "- Analysis (correlation, deaths, overdose)\n"
                      "- Demographics (age, sex, county, year)")

    return True, ""

def parse_query(text):
    """Enhanced query parser supporting multiple visualization types, demographics, and correlations"""
    text_lower = text.lower()
    query = {"original_text": text_lower}

    # Extract year(s) - fix regex to capture full 4-digit years
    year_matches = re.findall(r"(19\d{2}|20\d{2})", text)
    if year_matches:
        query["years"] = [int(year) for year in year_matches]
        query["year"] = query["years"][0]  # For backward compatibility
    else:
        query["years"] = None
        query["year"] = None

    # Extract drugs - support multiple drugs and handle fentanyl vs fentanylanalogs separately
    found_drugs = []

    # First, check for exact matches
    for drug in drug_columns:
        if drug in text_lower:
            found_drugs.append(drug)

    # If no exact matches, try partial matches for common drug names
    if not found_drugs:
        drug_aliases = {
            'fentanyl': ['fentanyl', 'fentanil', 'fentanill'],  # Added common misspellings
            'cocaine': ['cocaine', 'coke', 'cocane', 'cocain'],  # Added common misspellings
            'heroin': ['heroin', 'heroine', 'herion'],
            'methamphetamine': ['methamphetamine', 'meth', 'methamphetemine', 'methamphetamin'],
            'oxycodone': ['oxycodone', 'oxy', 'oxycodon', 'oxicodone'],
            'morphine': ['morphine', 'morfine', 'morphin'],
            'tramadol': ['tramadol', 'tramadoll', 'tramdol'],
            'alprazolam': ['alprazolam', 'xanax', 'alprazalam'],
            'diazepam': ['diazepam', 'valium', 'diazapam'],
            'ethanol': ['ethanol', 'alcohol', 'ethenol'],
            'amphetamine': ['amphetamine', 'amphetamin', 'amfetamine'],
            'hydrocodone': ['hydrocodone', 'hydrocodon', 'hydrocoden'],
            'codeine': ['codeine', 'codein', 'codiene']
        }

        for drug in drug_columns:
            drug_lower = drug.lower()
            # Check if any alias matches
            for alias_drug, aliases in drug_aliases.items():
                if alias_drug in drug_lower:
                    for alias in aliases:
                        if alias in text_lower:
                            found_drugs.append(drug)
                            break
                    if drug in found_drugs:
                        break

    # If still no matches, try fuzzy matching for common drugs
    if not found_drugs:
        # Extract potential drug words from the query
        words = re.findall(r'\b[a-zA-Z]{4,}\b', text_lower)  # Words with 4+ letters

        for word in words:
            # Try fuzzy matching against drug names
            fuzzy_match = fuzzy_match_drug(word, drug_columns, threshold=0.7)
            if fuzzy_match and fuzzy_match not in found_drugs:
                found_drugs.append(fuzzy_match)

    # Special handling for fentanyl vs fentanylanalogs based on user memory
    if 'fentanyl' in text_lower and 'analog' not in text_lower:
        found_drugs = [d for d in found_drugs if d == 'fentanyl']
    elif 'fentanylanalogs' in text_lower or ('fentanyl' in text_lower and 'analog' in text_lower):
        found_drugs = [d for d in found_drugs if d == 'fentanylanalogs']

    query["drugs"] = found_drugs if found_drugs else None
    query["drug"] = found_drugs[0] if found_drugs else None  # For backward compatibility

    # Parse cause vs present status
    cause_keywords = ["cause", "caused", "causing", "due to", "from"]
    present_keywords = ["present", "detected", "found", "positive", "with"]

    query["cause"] = any(word in text_lower for word in cause_keywords)
    query["present"] = any(word in text_lower for word in present_keywords)

    # Special handling for common phrases
    if "deaths caused by" in text_lower or "deaths from" in text_lower:
        query["cause"] = True
        query["present"] = False
    elif "deaths with" in text_lower or "positive for" in text_lower:
        query["present"] = True
        query["cause"] = False

    if query["cause"] and query["present"]:
        query["status"] = ["C", "P"]
    elif query["cause"]:
        query["status"] = ["C"]
    elif query["present"]:
        query["status"] = ["P"]
    else:
        query["status"] = ["C", "P"]  # Default to both

    # Extract demographics
    query["demographics"] = {}

    # Age groups
    age_group_keywords = {
        "teens": ["<18", "18-24"],
        "young adults": ["18-24", "25-34"],
        "middle-aged": ["35-44", "45-54"],
        "elderly": ["55-64", "65+"]
    }

    for age_desc, age_groups in age_group_keywords.items():
        if age_desc in text_lower:
            query["demographics"]["age_group"] = age_groups
            break

    # Sex
    if "male" in text_lower and "female" not in text_lower:
        query["demographics"]["sex"] = "M"
    elif "female" in text_lower and "male" not in text_lower:
        query["demographics"]["sex"] = "F"

    # Race
    race_keywords = ["white", "black", "hispanic", "asian", "other"]
    for race in race_keywords:
        if race in text_lower:
            query["demographics"]["race"] = race.title()
            break

    # Counties
    counties = df["county name"].dropna().unique()
    found_county = next((c for c in counties if c.lower() in text_lower), None)
    if found_county:
        query["demographics"]["county"] = found_county

    # Determine visualization intent
    if "pie chart" in text_lower or "pie" in text_lower:
        query["intent"] = "pie_chart"
    elif "bar chart" in text_lower or "bar" in text_lower:
        query["intent"] = "bar_chart"
    elif "line chart" in text_lower or "line" in text_lower or "trend" in text_lower:
        query["intent"] = "line_chart"
    elif "heatmap" in text_lower or "heat map" in text_lower:
        query["intent"] = "heatmap"
    elif "scatter" in text_lower or "scatterplot" in text_lower:
        query["intent"] = "scatter_plot"
    elif "map" in text_lower:
        query["intent"] = "map"
    elif "correlation" in text_lower or "corr" in text_lower:
        query["intent"] = "correlation"
        # For correlation queries, we need to be more aggressive about finding drugs
        # Reset the drug search to find ALL drugs mentioned in correlation context
        correlation_drugs = []
        for drug in drug_columns:
            if drug.lower() in text_lower:
                correlation_drugs.append(drug)

        # Also check aliases for correlation queries
        drug_aliases = {
            'fentanyl': ['fentanyl'],
            'cocaine': ['cocaine', 'coke'],
            'heroin': ['heroin'],
            'methamphetamine': ['methamphetamine', 'meth'],
            'oxycodone': ['oxycodone', 'oxy'],
            'morphine': ['morphine'],
            'tramadol': ['tramadol'],
            'alprazolam': ['alprazolam', 'xanax'],
            'diazepam': ['diazepam', 'valium'],
            'ethanol': ['ethanol', 'alcohol'],
            'amphetamine': ['amphetamine'],
            'hydrocodone': ['hydrocodone'],
            'codeine': ['codeine']
        }

        for drug in drug_columns:
            drug_lower = drug.lower()
            for alias_drug, aliases in drug_aliases.items():
                if alias_drug in drug_lower:
                    for alias in aliases:
                        if alias in text_lower and drug not in correlation_drugs:
                            correlation_drugs.append(drug)
                            break

        # Override the drugs found for correlation queries
        if correlation_drugs:
            query["drugs"] = correlation_drugs
            query["drug"] = correlation_drugs[0]

    elif ("generate" in text_lower and "table" in text_lower) or ("drug table" in text_lower) or ("overdose table" in text_lower):
        query["intent"] = "drug_summary_table"
    elif ("breakdown" in text_lower or "broken down" in text_lower or "by race" in text_lower or "by sex" in text_lower or "by age" in text_lower or "by county" in text_lower) and any(demo in text_lower for demo in ["race", "sex", "age", "county", "gender"]):
        query["intent"] = "demographic_breakdown"

    elif "most" in text_lower and "county" in text_lower:
        query["intent"] = "most_deaths_by_county"
    elif "least" in text_lower or "fewest" in text_lower:
        query["intent"] = "least_deaths_by_county"
    elif "most" in text_lower and "year" in text_lower:
        query["intent"] = "most_deaths_by_year"
    elif "first" in text_lower or "earliest" in text_lower:
        query["intent"] = "first_appearance"
    elif "table" in text_lower:
        query["intent"] = "table"
    else:
        query["intent"] = "count"

    # Extract grouping variable for visualizations
    group_by_keywords = {
        "by sex": "sex",
        "by gender": "sex",
        "by race": "race",
        "by age": "age_group",
        "by age group": "age_group",
        "by county": "county name",
        "by year": "year",
        "by manner": "manner of death"
    }

    for keyword, column in group_by_keywords.items():
        if keyword in text_lower:
            query["group_by"] = column
            break

    return query

def apply_filters(df_input, query):
    """Apply all filters based on the query"""
    dff = df_input.copy()

    # Apply year filter
    if query.get("years"):
        dff = dff[dff['year'].isin(query["years"])]
    elif query.get("year"):
        dff = dff[dff['year'] == query["year"]]

    # Apply drug filters
    if query.get("drugs"):
        drug_conditions = []
        for drug in query["drugs"]:
            if drug in dff.columns:
                condition = dff[drug].isin(query["status"])
                drug_conditions.append(condition)

        if drug_conditions:
            # Combine drug conditions with OR logic
            combined_condition = drug_conditions[0]
            for condition in drug_conditions[1:]:
                combined_condition = combined_condition | condition
            dff = dff[combined_condition]
    elif query.get("drug") and query["drug"] in dff.columns:
        dff = dff[dff[query["drug"]].isin(query["status"])]

    # Apply demographic filters
    demographics = query.get("demographics", {})

    if "sex" in demographics:
        dff = dff[dff["sex"] == demographics["sex"]]

    if "race" in demographics:
        dff = dff[dff["race"].str.contains(demographics["race"], case=False, na=False)]

    if "county" in demographics:
        dff = dff[dff["county name"].str.lower() == demographics["county"].lower()]

    if "age_group" in demographics:
        if isinstance(demographics["age_group"], list):
            dff = dff[dff["age_group"].isin(demographics["age_group"])]
        else:
            dff = dff[dff["age_group"] == demographics["age_group"]]

    return dff

def create_pie_chart(dff, query):
    """Create pie chart visualization"""
    group_by = query.get("group_by", "sex")  # Default to sex

    if group_by not in dff.columns:
        return f"❌ Cannot group by '{group_by}' - column not found."

    # Count occurrences
    pie_data = dff[group_by].value_counts()

    if pie_data.empty:
        return "📊 No data available for the pie chart."

    # Create title
    drug_text = f" for {', '.join(query['drugs'])}" if query.get("drugs") else ""
    status_text = f" ({'Cause' if 'C' in query['status'] else ''}{' & ' if len(query['status']) > 1 else ''}{'Present' if 'P' in query['status'] else ''})"
    title = f"Distribution by {group_by.title()}{drug_text}{status_text}"

    fig = px.pie(
        values=pie_data.values,
        names=pie_data.index,
        title=title,
        color_discrete_sequence=px.colors.qualitative.Set3
    )

    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(
        font=dict(size=14),
        title_font_size=16,
        showlegend=True
    )

    st.plotly_chart(fig, use_container_width=True)

    # Show summary statistics
    st.write(f"**Total cases:** {pie_data.sum():,}")
    st.write("**Breakdown:**")
    for category, count in pie_data.items():
        percentage = (count / pie_data.sum()) * 100
        st.write(f"- {category}: {count:,} ({percentage:.1f}%)")

    return None

def create_bar_chart(dff, query):
    """Create bar chart visualization"""
    group_by = query.get("group_by", "county name")  # Default to county

    if group_by not in dff.columns:
        return f"❌ Cannot group by '{group_by}' - column not found."

    # Count occurrences and get top 15 to avoid overcrowding
    bar_data = dff[group_by].value_counts().head(15)

    if bar_data.empty:
        return "📊 No data available for the bar chart."

    # Create title
    drug_text = f" for {', '.join(query['drugs'])}" if query.get("drugs") else ""
    status_text = f" ({'Cause' if 'C' in query['status'] else ''}{' & ' if len(query['status']) > 1 else ''}{'Present' if 'P' in query['status'] else ''})"
    title = f"Deaths by {group_by.title()}{drug_text}{status_text}"

    fig = px.bar(
        x=bar_data.index,
        y=bar_data.values,
        title=title,
        labels={'x': group_by.title(), 'y': 'Number of Deaths'},
        color=bar_data.values,
        color_continuous_scale='Viridis'
    )

    fig.update_layout(
        xaxis_tickangle=-45,
        font=dict(size=12),
        title_font_size=16,
        height=500
    )

    st.plotly_chart(fig, use_container_width=True)

    # Show top 5 summary
    st.write(f"**Top 5 {group_by}:**")
    for i, (category, count) in enumerate(bar_data.head(5).items(), 1):
        st.write(f"{i}. {category}: {count:,} deaths")

    return None

def create_line_chart(dff, query):
    """Create line chart visualization for trends over time"""
    if 'year' not in dff.columns:
        return "❌ Year column not found for line chart."

    # Group by year and count
    if query.get("group_by") and query["group_by"] != "year":
        # Multi-line chart grouped by another variable
        group_by = query["group_by"]
        if group_by not in dff.columns:
            return f"❌ Cannot group by '{group_by}' - column not found."

        line_data = dff.groupby(['year', group_by]).size().reset_index(name='count')

        # Create title
        drug_text = f" for {', '.join(query['drugs'])}" if query.get("drugs") else ""
        title = f"Trend Over Time by {group_by.title()}{drug_text}"

        fig = px.line(
            line_data,
            x='year',
            y='count',
            color=group_by,
            title=title,
            labels={'year': 'Year', 'count': 'Number of Deaths'}
        )
    else:
        # Simple line chart over time
        line_data = dff.groupby('year').size()

        # Create title
        drug_text = f" for {', '.join(query['drugs'])}" if query.get("drugs") else ""
        title = f"Trend Over Time{drug_text}"

        fig = px.line(
            x=line_data.index,
            y=line_data.values,
            title=title,
            labels={'x': 'Year', 'y': 'Number of Deaths'}
        )

    fig.update_layout(
        font=dict(size=12),
        title_font_size=16,
        height=500
    )

    st.plotly_chart(fig, use_container_width=True)

    # Show trend summary
    if len(line_data) > 1:
        if isinstance(line_data, pd.Series):
            first_year, last_year = line_data.index[0], line_data.index[-1]
            first_count, last_count = line_data.iloc[0], line_data.iloc[-1]
            change = last_count - first_count
            pct_change = (change / first_count) * 100 if first_count > 0 else 0

            st.write(f"**Trend Summary ({first_year}-{last_year}):**")
            st.write(f"- Start: {first_count:,} deaths")
            st.write(f"- End: {last_count:,} deaths")
            st.write(f"- Change: {change:+,} deaths ({pct_change:+.1f}%)")

    return None

def create_heatmap(dff, query):
    """Create heatmap visualization for drug co-occurrence or demographic patterns"""
    if query.get("drugs") and len(query["drugs"]) > 1:
        # Drug co-occurrence heatmap
        drug_subset = query["drugs"]

        # Create binary matrix for drug presence
        drug_matrix = pd.DataFrame()
        for drug in drug_subset:
            if drug in dff.columns:
                drug_matrix[drug] = (dff[drug].isin(query["status"])).astype(int)

        if drug_matrix.empty:
            return "❌ No valid drugs found for heatmap."

        # Calculate correlation matrix
        corr_matrix = drug_matrix.corr()

        fig = px.imshow(
            corr_matrix,
            title="Drug Co-occurrence Correlation Heatmap",
            color_continuous_scale='RdBu_r',
            aspect='auto'
        )

        fig.update_layout(
            font=dict(size=12),
            title_font_size=16,
            height=500
        )

        st.plotly_chart(fig, use_container_width=True)

        # Show strongest correlations
        st.write("**Strongest Correlations:**")
        corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if not np.isnan(corr_val):
                    corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_val))

        corr_pairs.sort(key=lambda x: abs(x[2]), reverse=True)
        for drug1, drug2, corr in corr_pairs[:5]:
            st.write(f"- {drug1} & {drug2}: {corr:.3f}")

    else:
        # Demographic heatmap
        group_by1 = query.get("group_by", "age_group")
        group_by2 = "sex" if group_by1 != "sex" else "race"

        if group_by1 not in dff.columns or group_by2 not in dff.columns:
            return f"❌ Cannot create heatmap with '{group_by1}' and '{group_by2}' - columns not found."

        # Create pivot table
        heatmap_data = dff.groupby([group_by1, group_by2]).size().reset_index(name='count')
        pivot_data = heatmap_data.pivot(index=group_by1, columns=group_by2, values='count').fillna(0)

        fig = px.imshow(
            pivot_data,
            title=f"Deaths by {group_by1.title()} and {group_by2.title()}",
            color_continuous_scale='Viridis',
            aspect='auto'
        )

        fig.update_layout(
            font=dict(size=12),
            title_font_size=16,
            height=500
        )

        st.plotly_chart(fig, use_container_width=True)

        # Show summary
        st.write(f"**Total deaths by {group_by1} and {group_by2}:**")
        st.dataframe(pivot_data)

    return None

def create_scatter_plot(dff, query):
    """Create scatter plot for correlation analysis"""
    # Default variables for scatter plot
    x_var = "age"
    y_var = "totalsubstancecount"

    # Try to extract variables from query
    if "age" in query.get("group_by", ""):
        x_var = "age"
    elif query.get("group_by") in aggregate_columns:
        y_var = query["group_by"]

    if x_var not in dff.columns or y_var not in dff.columns:
        return f"❌ Cannot create scatter plot with '{x_var}' and '{y_var}' - columns not found."

    # Remove missing values
    scatter_data = dff[[x_var, y_var]].dropna()

    if scatter_data.empty:
        return "📊 No data available for scatter plot."

    # Create title
    drug_text = f" for {', '.join(query['drugs'])}" if query.get("drugs") else ""
    title = f"Scatter Plot: {x_var.title()} vs {y_var.title()}{drug_text}"

    # Color by demographic if available
    color_var = None
    if query.get("group_by") and query["group_by"] in demographic_columns:
        color_var = query["group_by"]
    elif "sex" in dff.columns:
        color_var = "sex"

    if color_var and color_var in dff.columns:
        fig = px.scatter(
            scatter_data,
            x=x_var,
            y=y_var,
            color=dff[color_var],
            title=title,
            labels={x_var: x_var.title(), y_var: y_var.title()}
        )
    else:
        fig = px.scatter(
            scatter_data,
            x=x_var,
            y=y_var,
            title=title,
            labels={x_var: x_var.title(), y_var: y_var.title()}
        )

    fig.update_layout(
        font=dict(size=12),
        title_font_size=16,
        height=500
    )

    st.plotly_chart(fig, use_container_width=True)

    # Calculate correlation
    try:
        correlation, p_value = pearsonr(scatter_data[x_var], scatter_data[y_var])
        st.write(f"**Correlation Analysis:**")
        st.write(f"- Pearson correlation: {correlation:.3f}")
        st.write(f"- P-value: {p_value:.3f}")
        st.write(f"- Significance: {'Significant' if p_value < 0.05 else 'Not significant'} (α = 0.05)")
    except:
        st.write("Could not calculate correlation statistics.")

    return None

def create_map(dff, query):
    """Create geographic map visualization"""
    if not counties_geojson:
        return "🌐 Could not load Florida counties GeoJSON."

    map_df = dff.groupby("county name").size().reset_index(name="count")
    map_df["county name"] = map_df["county name"].str.title()

    # Create title
    drug_text = f" for {', '.join(query['drugs'])}" if query.get("drugs") else ""
    status_text = f" ({'Cause' if 'C' in query['status'] else ''}{' & ' if len(query['status']) > 1 else ''}{'Present' if 'P' in query['status'] else ''})"
    title = f"Florida County Map{drug_text}{status_text}"

    fig = px.choropleth(
        map_df,
        geojson=counties_geojson,
        locations="county name",
        featureidkey="properties.NAME",
        color="count",
        color_continuous_scale="Oranges",
        scope="usa",
        title=title
    )
    fig.update_geos(fitbounds="locations", visible=False)

    st.plotly_chart(fig, use_container_width=True)

    # Show top counties
    st.write("**Top 10 Counties:**")
    top_counties = map_df.nlargest(10, 'count')
    for i, (_, row) in enumerate(top_counties.iterrows(), 1):
        st.write(f"{i}. {row['county name']}: {row['count']:,} deaths")

    return None

def analyze_correlation(dff, query):
    """Analyze correlation between variables"""
    # Extract variables from query text
    variables = []
    text_lower = query.get("original_text", "").lower()

    # For drug-to-drug correlations, we need to extract multiple drugs from the text
    # even if the main query parsing only found some of them

    # First, try to find all drugs mentioned in the correlation query
    correlation_drugs = []

    # Look for drug names in the text, not just the parsed drugs
    for drug in drug_columns:
        if drug.lower() in text_lower:
            correlation_drugs.append(drug)

    # Also check for common drug aliases
    drug_aliases = {
        'fentanyl': ['fentanyl'],
        'cocaine': ['cocaine', 'coke'],
        'heroin': ['heroin'],
        'methamphetamine': ['methamphetamine', 'meth'],
        'oxycodone': ['oxycodone', 'oxy'],
        'morphine': ['morphine'],
        'tramadol': ['tramadol'],
        'alprazolam': ['alprazolam', 'xanax'],
        'diazepam': ['diazepam', 'valium'],
        'ethanol': ['ethanol', 'alcohol'],
        'amphetamine': ['amphetamine'],
        'hydrocodone': ['hydrocodone'],
        'codeine': ['codeine']
    }

    for drug in drug_columns:
        drug_lower = drug.lower()
        for alias_drug, aliases in drug_aliases.items():
            if alias_drug in drug_lower:
                for alias in aliases:
                    if alias in text_lower and drug not in correlation_drugs:
                        correlation_drugs.append(drug)
                        break

    # Add drug variables to correlation analysis
    for drug in correlation_drugs:
        if drug in dff.columns:
            # Convert drug columns to binary (present/not present)
            drug_binary = (dff[drug].isin(query["status"])).astype(int)
            variables.append((drug, drug_binary))

    # Check for demographic/numeric variables mentioned in the query
    numeric_cols = ['age', 'causesubstancecount', 'presentsubstancecount', 'totalsubstancecount']
    for col in numeric_cols:
        if col in text_lower and col in dff.columns:
            variables.append((col, dff[col]))

    # Special handling for common correlation queries
    if "age" in text_lower and not any(var[0] == 'age' for var in variables):
        # User wants to correlate something with age
        if 'age' in dff.columns:
            variables.append(('age', dff['age']))

    if ("substance" in text_lower and "count" in text_lower) and not any('count' in var[0] for var in variables):
        # User wants to correlate with substance count
        if 'totalsubstancecount' in dff.columns:
            variables.append(('totalsubstancecount', dff['totalsubstancecount']))

    if len(variables) < 2:
        # Provide more helpful error message
        found_drugs = [var[0] for var in variables if var[0] in drug_columns]
        error_msg = f"❌ Need at least 2 variables for correlation analysis.\n\n"
        if found_drugs:
            error_msg += f"Found drugs: {', '.join(found_drugs)}\n"
        error_msg += f"Available drugs in query: {', '.join(correlation_drugs) if correlation_drugs else 'None detected'}\n\n"
        error_msg += "**Try these examples:**\n"
        error_msg += "- 'correlation between fentanyl and cocaine'\n"
        error_msg += "- 'correlation between age and fentanyl'\n"
        error_msg += "- 'correlation between heroin and methamphetamine'"
        return error_msg

    st.write("### 🔗 Correlation Analysis")

    # Create correlation matrix
    corr_data = pd.DataFrame()
    for var_name, var_data in variables:
        corr_data[var_name] = var_data

    # Remove missing values
    corr_data = corr_data.dropna()

    if corr_data.empty:
        return "📊 No data available for correlation analysis."

    # Calculate correlation matrix
    corr_matrix = corr_data.corr()

    # Display correlation heatmap
    fig = px.imshow(
        corr_matrix,
        title="Correlation Matrix",
        color_continuous_scale='RdBu_r',
        aspect='auto'
    )

    fig.update_layout(
        font=dict(size=12),
        title_font_size=16,
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

    # Show correlation statistics
    st.write("**Correlation Coefficients:**")
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            var1, var2 = corr_matrix.columns[i], corr_matrix.columns[j]
            corr_val = corr_matrix.iloc[i, j]

            if not np.isnan(corr_val):
                # Calculate p-value if possible
                try:
                    _, p_val = pearsonr(corr_data[var1], corr_data[var2])
                    significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
                    st.write(f"- {var1} & {var2}: {corr_val:.3f}{significance} (p={p_val:.3f})")
                except:
                    st.write(f"- {var1} & {var2}: {corr_val:.3f}")

    st.write("*Significance: *** p<0.001, ** p<0.01, * p<0.05")

    return None

def generate_drug_summary_table(dff, query):
    """Generate a comprehensive table showing caused deaths vs present cases for all drugs"""

    # Create the summary table
    drug_summary = []

    for drug in sorted(drug_columns):
        if drug in dff.columns:
            # Count caused deaths (C)
            caused_count = (dff[drug] == 'C').sum()

            # Count present cases (P)
            present_count = (dff[drug] == 'P').sum()

            # Only include drugs that have at least some data
            if caused_count > 0 or present_count > 0:
                drug_summary.append({
                    'Drug': drug.title(),
                    'Caused Deaths': caused_count,
                    'Cases with Drug Present': present_count,
                    'Total Cases': caused_count + present_count
                })

    if not drug_summary:
        return "❌ No drug data found for the specified criteria."

    # Convert to DataFrame
    summary_df = pd.DataFrame(drug_summary)

    # Sort by total cases descending
    summary_df = summary_df.sort_values('Total Cases', ascending=False)

    # Create title based on filters
    title_parts = ["Drug Overdose Summary Table"]

    if query.get("year"):
        title_parts.append(f"for {query['year']}")
    elif query.get("years"):
        if len(query["years"]) == 1:
            title_parts.append(f"for {query['years'][0]}")
        else:
            title_parts.append(f"for {min(query['years'])}-{max(query['years'])}")

    if query.get("demographics", {}).get("county"):
        title_parts.append(f"in {query['demographics']['county']}")

    title = " ".join(title_parts)

    st.subheader(f"📋 {title}")

    # Display the table
    st.dataframe(
        summary_df,
        use_container_width=True,
        hide_index=True,
        column_config={
            "Drug": st.column_config.TextColumn("Drug", width="medium"),
            "Caused Deaths": st.column_config.NumberColumn("Caused Deaths", format="%d"),
            "Cases with Drug Present": st.column_config.NumberColumn("Cases with Drug Present", format="%d"),
            "Total Cases": st.column_config.NumberColumn("Total Cases", format="%d")
        }
    )

    # Show summary statistics
    total_caused = summary_df['Caused Deaths'].sum()
    total_present = summary_df['Cases with Drug Present'].sum()
    total_overall = summary_df['Total Cases'].sum()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Drugs", len(summary_df))
    with col2:
        st.metric("Total Caused Deaths", f"{total_caused:,}")
    with col3:
        st.metric("Total Present Cases", f"{total_present:,}")
    with col4:
        st.metric("Total Cases", f"{total_overall:,}")

    # Show top 10 drugs
    st.write("### 🔝 Top 10 Drugs by Total Cases")
    top_10 = summary_df.head(10)

    for i, (_, row) in enumerate(top_10.iterrows(), 1):
        caused_pct = (row['Caused Deaths'] / row['Total Cases'] * 100) if row['Total Cases'] > 0 else 0
        present_pct = (row['Cases with Drug Present'] / row['Total Cases'] * 100) if row['Total Cases'] > 0 else 0

        st.write(f"**{i}. {row['Drug']}**: {row['Total Cases']:,} total cases")
        st.write(f"   - Caused deaths: {row['Caused Deaths']:,} ({caused_pct:.1f}%)")
        st.write(f"   - Present cases: {row['Cases with Drug Present']:,} ({present_pct:.1f}%)")

    # Option to download the data
    csv = summary_df.to_csv(index=False)
    st.download_button(
        label="📥 Download Table as CSV",
        data=csv,
        file_name=f"drug_summary_table_{query.get('year', 'all_years')}.csv",
        mime="text/csv"
    )

    return None

def create_demographic_breakdown(dff, query):
    """Create demographic breakdown analysis with both charts and tables"""

    # Determine which demographic to break down by
    text_lower = query.get("original_text", "").lower()

    # Determine demographic variable
    demographic_var = None
    if "by race" in text_lower or "race" in text_lower:
        demographic_var = "race"
        demo_title = "Race"
    elif "by sex" in text_lower or "by gender" in text_lower or "sex" in text_lower:
        demographic_var = "sex"
        demo_title = "Sex"
    elif "by age" in text_lower or "age group" in text_lower:
        demographic_var = "age_group"
        demo_title = "Age Group"
    elif "by county" in text_lower or "county" in text_lower:
        demographic_var = "county name"
        demo_title = "County"
    else:
        # Default to race if no specific demographic mentioned
        demographic_var = "race"
        demo_title = "Race"

    # Check if demographic column exists
    if demographic_var not in dff.columns:
        return f"❌ {demo_title} data not available in the dataset."

    # Get drug information
    drug_name = "Unknown Drug"
    if query.get("drugs") and len(query["drugs"]) > 0:
        drug_name = query["drugs"][0].title()

    # Create title
    title = f"{drug_name} Overdoses by {demo_title}"
    if query.get("year"):
        title += f" in {query['year']}"

    st.subheader(f"📊 {title}")

    # Create the breakdown data
    breakdown_data = dff.groupby(demographic_var).size().reset_index(name='Count')
    breakdown_data = breakdown_data.sort_values('Count', ascending=False)

    # Remove null/empty values
    breakdown_data = breakdown_data[breakdown_data[demographic_var].notna()]
    breakdown_data = breakdown_data[breakdown_data[demographic_var] != '']

    if breakdown_data.empty:
        return f"❌ No data available for {drug_name} breakdown by {demo_title.lower()}."

    # Create two columns for chart and table
    col1, col2 = st.columns([2, 1])

    with col1:
        st.write("### 📈 Bar Chart")

        # Create bar chart
        fig = px.bar(
            breakdown_data,
            x=demographic_var,
            y='Count',
            title=f"{drug_name} Deaths by {demo_title}",
            color='Count',
            color_continuous_scale='Viridis'
        )

        fig.update_layout(
            xaxis_title=demo_title,
            yaxis_title="Number of Deaths",
            showlegend=False
        )

        # Rotate x-axis labels if needed
        if len(breakdown_data) > 5:
            fig.update_xaxes(tickangle=45)

        st.plotly_chart(fig, use_container_width=True)

    with col2:
        st.write("### 📋 Data Table")

        # Format the table
        breakdown_display = breakdown_data.copy()
        breakdown_display.columns = [demo_title, 'Deaths']

        # Add percentage column
        total_deaths = breakdown_display['Deaths'].sum()
        breakdown_display['Percentage'] = (breakdown_display['Deaths'] / total_deaths * 100).round(1)
        breakdown_display['Percentage'] = breakdown_display['Percentage'].astype(str) + '%'

        st.dataframe(
            breakdown_display,
            use_container_width=True,
            hide_index=True
        )

    # Summary statistics
    st.write("### 📈 Summary Statistics")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Deaths", f"{total_deaths:,}")

    with col2:
        most_affected = breakdown_data.iloc[0]
        st.metric(f"Most Affected {demo_title}", f"{most_affected[demographic_var]}")

    with col3:
        st.metric("Highest Count", f"{most_affected['Count']:,}")

    with col4:
        highest_pct = (most_affected['Count'] / total_deaths * 100)
        st.metric("Highest Percentage", f"{highest_pct:.1f}%")

    # Detailed breakdown
    st.write(f"### 🔍 Detailed {demo_title} Breakdown")

    for i, (_, row) in enumerate(breakdown_data.iterrows(), 1):
        percentage = (row['Count'] / total_deaths * 100)
        st.write(f"**{i}. {row[demographic_var]}**: {row['Count']:,} deaths ({percentage:.1f}%)")

    # Additional analysis if multiple drugs
    if query.get("drugs") and len(query["drugs"]) > 1:
        st.write(f"### 🔬 Multi-Drug Analysis by {demo_title}")

        # Create comparison across drugs
        multi_drug_data = []
        for drug in query["drugs"]:
            if drug in dff.columns:
                drug_subset = dff[dff[drug].isin(query["status"])]
                drug_breakdown = drug_subset.groupby(demographic_var).size().reset_index(name='Count')
                drug_breakdown['Drug'] = drug.title()
                multi_drug_data.append(drug_breakdown)

        if multi_drug_data:
            combined_data = pd.concat(multi_drug_data, ignore_index=True)

            # Create grouped bar chart
            fig_multi = px.bar(
                combined_data,
                x=demographic_var,
                y='Count',
                color='Drug',
                title=f"Multiple Drug Comparison by {demo_title}",
                barmode='group'
            )

            fig_multi.update_layout(
                xaxis_title=demo_title,
                yaxis_title="Number of Deaths"
            )

            if len(combined_data[demographic_var].unique()) > 5:
                fig_multi.update_xaxes(tickangle=45)

            st.plotly_chart(fig_multi, use_container_width=True)

    # Download option
    csv = breakdown_data.to_csv(index=False)
    st.download_button(
        label=f"📥 Download {demo_title} Breakdown as CSV",
        data=csv,
        file_name=f"{drug_name.lower()}_{demographic_var}_breakdown.csv",
        mime="text/csv"
    )

    return None

def handle_query(query):
    """Main query handler that routes to appropriate visualization or analysis"""
    # Apply filters to get the relevant data
    dff = apply_filters(df, query)

    if dff.empty:
        # Provide more helpful error message
        error_msg = "❌ No data matches your query criteria.\n\n"

        if query.get("drugs"):
            error_msg += f"**Drugs searched:** {', '.join(query['drugs'])}\n"
            error_msg += f"**Status:** {', '.join(query['status'])}\n"

            # Check if drugs exist in dataset
            missing_drugs = [drug for drug in query['drugs'] if drug not in df.columns]
            if missing_drugs:
                error_msg += f"**⚠️ Drugs not found in dataset:** {', '.join(missing_drugs)}\n"
                available_similar = []
                for missing in missing_drugs:
                    similar = [col for col in drug_columns if missing.lower() in col.lower() or col.lower() in missing.lower()]
                    if similar:
                        available_similar.extend(similar[:3])  # Show up to 3 similar
                if available_similar:
                    error_msg += f"**💡 Similar drugs available:** {', '.join(set(available_similar))}\n"

        if query.get("year"):
            error_msg += f"**Year:** {query['year']}\n"
            available_years = sorted(df['year'].dropna().unique())
            error_msg += f"**Available years:** {min(available_years)}-{max(available_years)}\n"

        if query.get("demographics"):
            error_msg += f"**Demographics:** {query['demographics']}\n"

        error_msg += "\n**💡 Try:**\n"
        error_msg += "- Using different drug names (check debug section below)\n"
        error_msg += "- Different years or removing year filter\n"
        error_msg += "- Simpler queries like 'fentanyl deaths in 2022'\n"

        return error_msg

    # Route to appropriate handler based on intent
    intent = query.get("intent", "count")

    if intent == "pie_chart":
        return create_pie_chart(dff, query)
    elif intent == "bar_chart":
        return create_bar_chart(dff, query)
    elif intent == "line_chart":
        return create_line_chart(dff, query)
    elif intent == "heatmap":
        return create_heatmap(dff, query)
    elif intent == "scatter_plot":
        return create_scatter_plot(dff, query)
    elif intent == "map":
        return create_map(dff, query)
    elif intent == "correlation":
        return analyze_correlation(dff, query)
    elif intent == "drug_summary_table":
        return generate_drug_summary_table(dff, query)
    elif intent == "demographic_breakdown":
        return create_demographic_breakdown(dff, query)
    elif intent == "most_deaths_by_county":
        result = dff["county name"].value_counts().head(1)
        if not result.empty:
            return f"🏆 {result.index[0]} with {result.iloc[0]:,} deaths"
        else:
            return "No results found."
    elif intent == "least_deaths_by_county":
        result = dff["county name"].value_counts().sort_values().head(1)
        if not result.empty:
            return f"📉 {result.index[0]} had the fewest deaths ({result.iloc[0]:,})"
        else:
            return "No results found."
    elif intent == "most_deaths_by_year":
        result = dff["year"].value_counts().head(1)
        if not result.empty:
            return f"📅 {result.index[0]} had the most cases ({result.iloc[0]:,})"
        else:
            return "No results found."
    elif intent == "first_appearance":
        if query.get("drug") and query["drug"] in dff.columns:
            first_year = df[df[query["drug"]].isin(query["status"])]["year"].min()
            return f"🕐 First appearance of {query['drug'].title()} ({' or '.join(query['status'])}) was in {first_year}."
        else:
            return "❌ Need to specify a drug for first appearance query."
    elif intent == "table":
        st.write("### 📋 Data Table")
        st.dataframe(dff.head(100))  # Show first 100 rows
        return f"Showing first 100 rows of {len(dff):,} matching records."
    else:  # count
        return f"✅ {len(dff):,} matching cases found."

# Main execution
if user_input:
    # First validate the query
    is_valid, error_message = validate_query(user_input)

    if not is_valid:
        st.error(error_message)
        st.info("💡 **Try these example queries:**")
        st.write("- 'Deaths caused by fentanyl in 2022'")
        st.write("- 'Pie chart of cocaine deaths by sex'")
        st.write("- 'Correlation between age and heroin'")
        st.write("- 'Generate a table of drug overdoses for 2023'")
        st.write("- 'Map of methamphetamine deaths in Florida'")
    else:
        query = parse_query(user_input)

        # Debug information (optional - can be toggled)
        with st.expander("🔍 Debug: Query Analysis", expanded=False):
            st.write("**Parsed Query:**")
            st.json({
                "intent": query.get("intent"),
                "drugs": query.get("drugs"),
                "status": query.get("status"),
                "year": query.get("year"),
                "demographics": query.get("demographics"),
                "group_by": query.get("group_by")
            })

            # Show available drugs for reference
            st.write("**Available drugs in dataset:**")
            st.write(f"Total drug columns: {len(drug_columns)}")
            sample_drugs = sorted(drug_columns)[:20]  # Show first 20 alphabetically
            st.write(f"Sample: {', '.join(sample_drugs)}")

        # Show fuzzy matching suggestions if applicable
        if query.get("drugs"):
            original_words = re.findall(r'\b[a-zA-Z]{4,}\b', user_input.lower())
            suggestions = []
            for word in original_words:
                for drug in query["drugs"]:
                    if word != drug.lower() and word in drug.lower():
                        suggestions.append(f"'{word}' → '{drug}'")

            if suggestions:
                st.info(f"🔍 **Fuzzy matching applied:** {', '.join(suggestions)}")

        response = handle_query(query)
        if response:
            st.success(response)
