#!/usr/bin/env python3
"""
Test the new demographic breakdown feature
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import parse_query, validate_query, apply_filters, df, drug_columns
    
    print("🧪 TESTING DEMOGRAPHIC BREAKDOWN FEATURE")
    print("=" * 60)
    
    # Test demographic breakdown queries
    print("\n📝 Test 1: Demographic Breakdown Query Parsing")
    print("-" * 50)
    
    demographic_queries = [
        "Fentanyl overdoses by race",
        "Cocaine deaths broken down by sex", 
        "Heroin overdoses by age group",
        "Methamphetamine deaths by county",
        "Oxycodone overdoses breakdown by race in 2022",
        "Deaths caused by fentanyl by sex in Miami-Dade"
    ]
    
    for query_text in demographic_queries:
        print(f"\n   Query: '{query_text}'")
        
        # Validate
        is_valid, error_msg = validate_query(query_text)
        print(f"   Validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if is_valid:
            # Parse
            query = parse_query(query_text)
            print(f"   Intent: {query.get('intent')}")
            print(f"   Drugs: {query.get('drugs')}")
            print(f"   Year: {query.get('year')}")
            print(f"   Status: {query.get('status')}")
            
            # Test filtering
            filtered_df = apply_filters(df, query)
            print(f"   Filtered records: {len(filtered_df):,}")
    
    # Test 2: Check available demographic data
    print(f"\n📝 Test 2: Available Demographic Data")
    print("-" * 50)
    
    demographic_columns = ['race', 'sex', 'age_group', 'county name']
    
    for col in demographic_columns:
        if col in df.columns:
            unique_values = df[col].value_counts().head(10)
            print(f"\n   {col.title()}:")
            for value, count in unique_values.items():
                if pd.notna(value) and value != '':
                    print(f"     {value}: {count:,} records")
        else:
            print(f"\n   {col.title()}: ❌ Column not found")
    
    # Test 3: Sample demographic breakdown
    print(f"\n📝 Test 3: Sample Demographic Breakdown")
    print("-" * 50)
    
    # Test with fentanyl by race
    query_text = "Fentanyl overdoses by race"
    query = parse_query(query_text)
    filtered_df = apply_filters(df, query)
    
    print(f"   Query: '{query_text}'")
    print(f"   Total records: {len(filtered_df):,}")
    
    if len(filtered_df) > 0 and 'race' in filtered_df.columns:
        race_breakdown = filtered_df['race'].value_counts().head(5)
        print(f"   Top 5 races affected:")
        for race, count in race_breakdown.items():
            if pd.notna(race) and race != '':
                percentage = (count / len(filtered_df) * 100)
                print(f"     {race}: {count:,} ({percentage:.1f}%)")
    
    # Test 4: Multi-drug demographic breakdown
    print(f"\n📝 Test 4: Multi-Drug Demographic Breakdown")
    print("-" * 50)
    
    multi_drug_query = "Fentanyl and cocaine overdoses by sex"
    query = parse_query(multi_drug_query)
    filtered_df = apply_filters(df, query)
    
    print(f"   Query: '{multi_drug_query}'")
    print(f"   Drugs found: {query.get('drugs')}")
    print(f"   Total records: {len(filtered_df):,}")
    
    if len(filtered_df) > 0 and 'sex' in filtered_df.columns:
        sex_breakdown = filtered_df['sex'].value_counts()
        print(f"   Sex breakdown:")
        for sex, count in sex_breakdown.items():
            if pd.notna(sex) and sex != '':
                percentage = (count / len(filtered_df) * 100)
                print(f"     {sex}: {count:,} ({percentage:.1f}%)")
    
    # Test 5: Year and county filtering with demographics
    print(f"\n📝 Test 5: Complex Filtering with Demographics")
    print("-" * 50)
    
    complex_query = "Cocaine deaths by race in 2022"
    query = parse_query(complex_query)
    filtered_df = apply_filters(df, query)
    
    print(f"   Query: '{complex_query}'")
    print(f"   Year: {query.get('year')}")
    print(f"   Drugs: {query.get('drugs')}")
    print(f"   Status: {query.get('status')}")
    print(f"   Total records: {len(filtered_df):,}")
    
    if len(filtered_df) > 0 and 'race' in filtered_df.columns:
        race_breakdown = filtered_df['race'].value_counts().head(3)
        print(f"   Top 3 races in 2022:")
        for race, count in race_breakdown.items():
            if pd.notna(race) and race != '':
                print(f"     {race}: {count:,}")
    
    print(f"\n🎉 DEMOGRAPHIC BREAKDOWN TESTING COMPLETED!")
    print("=" * 60)
    print("Summary of new features:")
    print("✅ Demographic breakdown query parsing works")
    print("✅ Multiple demographic variables supported (race, sex, age_group, county)")
    print("✅ Integration with existing filtering (year, drug, status)")
    print("✅ Multi-drug demographic analysis supported")
    print("✅ Complex queries with multiple filters work")
    
    print(f"\n💡 Example queries that now work:")
    print("- 'Fentanyl overdoses by race'")
    print("- 'Cocaine deaths broken down by sex in 2022'")
    print("- 'Heroin overdoses by age group'")
    print("- 'Methamphetamine deaths by county'")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
