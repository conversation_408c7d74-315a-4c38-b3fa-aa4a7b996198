#!/usr/bin/env python3
"""
Test the error handling and fuzzy matching fixes
"""

import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append('.')

try:
    from drug_query_chatbot_app import parse_query, validate_query, fuzzy_match_drug, handle_query, drug_columns, df
    
    print("🧪 TESTING ERROR HANDLING AND FUZZY MATCHING")
    print("=" * 60)
    
    # Test 1: Random strings that should be rejected
    print("\n📝 Test 1: Random/Invalid Queries")
    print("-" * 40)
    
    invalid_queries = [
        "as;dflkj",
        "error", 
        "qwerty",
        "asdf",
        "random",
        "test",
        "gibberish",
        "zzzzzzz",
        "bcdfgh",  # No vowels
        "",
        "a",
        "12345"
    ]
    
    for query_text in invalid_queries:
        is_valid, error_msg = validate_query(query_text)
        print(f"   '{query_text}': {'✅ Valid' if is_valid else '❌ Invalid'}")
        if not is_valid:
            print(f"      → {error_msg.split('.')[0]}...")  # Show first sentence only
    
    # Test 2: Fuzzy matching for drug names
    print(f"\n📝 Test 2: Fuzzy Matching for Drug Names")
    print("-" * 40)
    
    misspelled_drugs = [
        "fentanil",      # fentanyl
        "cocane",        # cocaine  
        "herione",       # heroin
        "meth",          # methamphetamine
        "morfine",       # morphine
        "oxicodone",     # oxycodone
        "tramadoll",     # tramadol
        "xanax",         # alprazolam
        "valium",        # diazepam
        "alcohol"        # ethanol
    ]
    
    for misspelled in misspelled_drugs:
        match = fuzzy_match_drug(misspelled, drug_columns, threshold=0.6)
        print(f"   '{misspelled}' → {match if match else 'No match'}")
    
    # Test 3: Full query processing with misspellings
    print(f"\n📝 Test 3: Full Query Processing with Misspellings")
    print("-" * 40)
    
    misspelled_queries = [
        "Deaths caused by fentanil in 2022",
        "How many cocane deaths in 2021?",
        "Pie chart of herione deaths by sex",
        "Correlation between age and morfine",
        "Bar chart of oxicodone deaths by county"
    ]
    
    for query_text in misspelled_queries:
        print(f"\n   Query: '{query_text}'")
        
        # Validate
        is_valid, error_msg = validate_query(query_text)
        print(f"   Validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if is_valid:
            # Parse
            query = parse_query(query_text)
            print(f"   Drugs found: {query.get('drugs')}")
            print(f"   Intent: {query.get('intent')}")
            
            # Test filtering (don't run full handler to avoid Streamlit issues)
            from drug_query_chatbot_app import apply_filters
            filtered_df = apply_filters(df, query)
            print(f"   Results: {len(filtered_df):,} records")
    
    # Test 4: Queries that should now be rejected instead of returning 217,929
    print(f"\n📝 Test 4: Queries That Should Be Rejected")
    print("-" * 40)
    
    should_reject = [
        "as;dflkj",
        "error",
        "random text",
        "hello world",
        "123456"
    ]
    
    for query_text in should_reject:
        print(f"\n   Query: '{query_text}'")
        
        is_valid, error_msg = validate_query(query_text)
        if is_valid:
            query = parse_query(query_text)
            # Simulate what would happen
            has_filters = (
                query.get("drugs") or 
                query.get("year") or 
                query.get("years") or 
                query.get("demographics")
            )
            
            if query.get("intent") == "count" and not has_filters:
                print(f"   Result: ✅ Would be rejected (no meaningful filters)")
            else:
                print(f"   Result: ⚠️  Would return data (intent: {query.get('intent')})")
        else:
            print(f"   Result: ✅ Rejected at validation stage")
    
    # Test 5: Valid queries that should still work
    print(f"\n📝 Test 5: Valid Queries That Should Still Work")
    print("-" * 40)
    
    valid_queries = [
        "Deaths caused by fentanyl in 2022",
        "How many cocaine deaths?",
        "Pie chart of heroin deaths by sex",
        "Generate a table of drug overdoses for 2023",
        "Correlation between age and methamphetamine"
    ]
    
    for query_text in valid_queries:
        print(f"\n   Query: '{query_text}'")
        
        is_valid, error_msg = validate_query(query_text)
        print(f"   Validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if is_valid:
            query = parse_query(query_text)
            print(f"   Intent: {query.get('intent')}")
            print(f"   Drugs: {query.get('drugs')}")
            print(f"   Year: {query.get('year')}")
    
    print(f"\n🎉 ERROR HANDLING AND FUZZY MATCHING TESTS COMPLETED!")
    print("=" * 60)
    print("Summary of fixes:")
    print("✅ Random strings are now rejected at validation stage")
    print("✅ Fuzzy matching works for common drug name misspellings")
    print("✅ Count queries without filters are rejected")
    print("✅ Valid queries still work as expected")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
